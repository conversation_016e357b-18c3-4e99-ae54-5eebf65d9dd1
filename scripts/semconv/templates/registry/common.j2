{%- macro file_header() -%}
# Copyright The OpenTelemetry Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

{% endmacro -%}

{%- macro str_or_empty(str) -%}
{% if str is none %}{{""}}{% else %}{{str}}{% endif %}
{%- endmacro %}

{%- macro remove_trailing_dots(str) -%}
{%- if str[-1:] == '.' -%}{{ remove_trailing_dots(str[:-1]) }}{%- else -%}{{ str }}{%- endif -%}
{%- endmacro -%}

{%- macro comment_with_prefix(str, prefix) -%}
{{remove_trailing_dots(str | trim(' \n')) | comment_with_prefix(prefix) | replace("\\", "\\\\")}}
{%- endmacro %}

{%- macro import_deprecated(semconv) -%}
    {%- if (semconv | select("deprecated") | list | count > 0) or (ctx.filter == "any" and semconv | select("stable") | list | count > 0) -%}
from deprecated import deprecated
    {%- endif -%}
{%- endmacro-%}

{%- macro deprecated_note_or_empty(attribute) -%}
{% if attribute is deprecated %}{{ attribute.deprecated.note }}{% else %}{{""}}{% endif %}
{%- endmacro %}
