# Copyright The OpenTelemetry Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from typing import Final

{% set file_name = ctx.output + (ctx.root_namespace | snake_case) ~ "_attributes.py" -%}
{{- template.set_file_name(file_name) -}}
{%- import 'common.j2' as c %}

{% set attributes = ctx.attributes | list %}
{% set enum_attributes = attributes | select("enum") | rejectattr("name", "in", ctx.excluded_attributes) | list %}
{% if enum_attributes | count > 0 %}from enum import Enum{% endif %}
{{c.import_deprecated(enum_attributes)}}

{%- macro attribute_name(attribute) -%}
{{ attribute.name | screaming_snake_case }}{%- if attribute.type is template_type -%}_TEMPLATE{%- endif -%}
{%- endmacro -%}

{%- macro stable_class_ref(const_name, separator) -%}
{{ctx.stable_package_name}}.{{ctx.root_namespace}}_attributes{{separator}}{{const_name}}
{%- endmacro %}

{%- macro write_docstring(name, brief, note, deprecated_note, stability, multiline) -%}
{%- if multiline %}"""
{% endif %}
    {%- if c.str_or_empty(deprecated_note)|length -%}
{{prefix}}Deprecated: {{c.comment_with_prefix(deprecated_note, "")}}.
    {%- elif ctx.filter == "any" and stability == "stable" -%}
{{prefix}}Deprecated in favor of stable :py:const:`{{stable_class_ref(name, '.')}}`.
    {%- elif c.str_or_empty(brief)|length -%}
{{prefix}}{{c.comment_with_prefix(brief, "")}}.
        {%- if c.str_or_empty(note)|length  %}
{{prefix}}Note: {{c.comment_with_prefix(note, "")}}.
        {%- endif -%}
    {%- endif -%}
{%- if multiline %}
"""{%- endif %}
{%- endmacro -%}

{% for attribute in attributes %}
{% set attr_name = attribute_name(attribute) %}
{%- set multiline = attribute.name not in ctx.excluded_attributes -%}
{%- set deprecated_note = c.deprecated_note_or_empty(attribute) %}
{%- set doc_string = write_docstring(attr_name, attribute.brief, attribute.note, deprecated_note, attribute.stability, multiline)-%}
{%- set prefix = "" if multiline else "# " -%}
{{prefix}}{{attr_name}}: Final = "{{attribute.name}}"
{{prefix}}{{doc_string}}
{% endfor %}

{% for attribute in enum_attributes %}{%- set class_name = attribute.name | map_text("py_enum_attribute_to_class_name", attribute.name | pascal_case ~ "Values") -%}
{%- if attribute is deprecated %}
@deprecated(reason="The attribute {{attribute.name}} is deprecated - {{ c.comment_with_prefix(attribute.deprecated.note, "") }}")  # type: ignore
    {%- elif attribute.stability == "stable" and ctx.filter == "any" %}
@deprecated(reason="Deprecated in favor of stable :py:const:`{{stable_class_ref(class_name, '.')}}`.")  # type: ignore
    {%- endif %}
class {{class_name}}(Enum):
    {%- for member in attribute.type.members %}
    {% set member_name = member.id | screaming_snake_case -%}
    {%- set doc_string=write_docstring(class_name + '.' + member_name, member.brief or member.id, "", member.deprecated, member.stability, false)-%}
    {{member_name}} = {{ member.value | print_member_value }}
    {% if doc_string %}"""{{doc_string}}"""{% endif %}
    {%- endfor %}
{% endfor %}
