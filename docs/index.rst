OpenTelemetry-Python API Reference
==================================

.. image:: https://img.shields.io/badge/slack-chat-green.svg
   :target: https://cloud-native.slack.com/archives/C01PD4HUVBL
   :alt: Slack Chat

Welcome to the docs for the `Python OpenTelemetry implementation
<https://opentelemetry.io/docs/instrumentation/python/>`_.

For an introduction to OpenTelemetry, see the `OpenTelemetry website docs
<https://opentelemetry.io/docs/>`_.

To learn how to instrument your Python code, see `Getting Started
<https://opentelemetry.io/docs/instrumentation/python/getting-started/>`_. For
project status, information about releases, installation instructions and more,
see `Python <https://opentelemetry.io/docs/instrumentation/python/>`_.

Getting Started
---------------

* `Getting Started <https://opentelemetry.io/docs/instrumentation/python/getting-started/>`_
* `Frequently Asked Questions and Cookbook <https://opentelemetry.io/docs/instrumentation/python/cookbook/>`_

.. toctree::
    :maxdepth: 1
    :caption: Core Packages
    :name: packages

    api/index
    sdk/index

.. toctree::
    :maxdepth: 2
    :caption: More
    :glob:

    exporter/index
    shim/index
    examples/index


* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
