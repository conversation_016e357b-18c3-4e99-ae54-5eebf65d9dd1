[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "error-handler-0"
dynamic = ["version"]
description = "This is just an error handler example package"
readme = "README.rst"
license = {text = "Apache-2.0"}
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
]
dependencies = [
  "opentelemetry-sdk ~= 1.3",
]

[project.entry-points.opentelemetry_error_handler]
error_handler_0 = "error_handler_0:ErrorHandler0"

[tool.hatch.version]
path = "src/error_handler_0/version/__init__.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
