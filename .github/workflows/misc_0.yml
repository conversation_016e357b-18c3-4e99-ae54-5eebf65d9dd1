# Do not edit this file.
# This file is generated automatically by executing tox -e generate-workflows

name: Misc 0

on:
  push:
    branches-ignore:
    - 'release/*'
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  CORE_REPO_SHA: main
  CONTRIB_REPO_SHA: main
  PIP_EXISTS_ACTION: w

jobs:

  spellcheck:
    name: spellcheck
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e spellcheck

  tracecontext:
    name: tracecontext
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e tracecontext

  mypy:
    name: mypy
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e mypy

  mypyinstalled:
    name: mypyinstalled
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e mypyinstalled

  typecheck:
    name: typecheck
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e typecheck

  docs:
    name: docs
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: |
      github.event.pull_request.user.login != 'opentelemetrybot' && github.event_name == 'pull_request'
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e docs

  docker-tests-otlpexporter:
    name: docker-tests-otlpexporter
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e docker-tests-otlpexporter

  docker-tests-opencensus:
    name: docker-tests-opencensus
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e docker-tests-opencensus

  public-symbols-check:
    name: public-symbols-check
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: |
      !contains(github.event.pull_request.labels.*.name, 'Approve Public API check')
      && github.actor != 'opentelemetrybot' && github.event_name == 'pull_request'
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Checkout main
        run: git checkout main

      - name: Pull origin
        run: git pull --rebase=false origin main

      - name: Checkout pull request
        run: git checkout ${{ github.event.pull_request.head.sha }}

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e public-symbols-check

  shellcheck:
    name: shellcheck
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e shellcheck

  generate-workflows:
    name: generate-workflows
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: |
      !contains(github.event.pull_request.labels.*.name, 'Skip generate-workflows')
      && github.event.pull_request.user.login != 'opentelemetrybot' && github.event_name == 'pull_request'
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e generate-workflows

      - name: Check github workflows are up to date
        run: git diff --exit-code || (echo 'Generated workflows are out of date, run "tox -e generate-workflows" and commit the changes in this PR.' && exit 1)

  precommit:
    name: precommit
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e precommit
