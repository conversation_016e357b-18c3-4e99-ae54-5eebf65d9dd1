# Do not edit this file.
# This file is generated automatically by executing tox -e generate-workflows

name: Test {{ file_number }}

on:
  push:
    branches-ignore:
    - 'release/*'
  pull_request:

concurrency:
  group: ${% raw %}{{ github.workflow }}-${{ github.head_ref || github.run_id }}{% endraw %}
  cancel-in-progress: true

env:
  CORE_REPO_SHA: main
  CONTRIB_REPO_SHA: main
  PIP_EXISTS_ACTION: w

jobs:
  {%- for job_data in job_datas %}

  {{ job_data.name }}:
    name: {{ job_data.ui_name }}
    runs-on: {{ job_data.os }}
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${% raw %}{{ github.sha }}{% endraw %}
        uses: actions/checkout@v4

      - name: Set up Python {{ job_data.python_version }}
        uses: actions/setup-python@v5
        with:
          python-version: "{{ job_data.python_version }}"

      - name: Install tox
        run: pip install tox
      {%- if job_data.os == "windows-latest" %}

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true
      {%- endif %}

      - name: Run tests
        run: tox -e {{ job_data.tox_env }} -- -ra
  {%- endfor %}
