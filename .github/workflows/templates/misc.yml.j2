# Do not edit this file.
# This file is generated automatically by executing tox -e generate-workflows

name: Misc {{ file_number }}

on:
  push:
    branches-ignore:
    - 'release/*'
  pull_request:

concurrency:
  group: ${% raw %}{{ github.workflow }}-${{ github.head_ref || github.run_id }}{% endraw %}
  cancel-in-progress: true

env:
  CORE_REPO_SHA: main
  CONTRIB_REPO_SHA: main
  PIP_EXISTS_ACTION: w

jobs:
  {%- for job_data in job_datas %}

  {{ job_data }}:
    name: {{ job_data }}
    runs-on: ubuntu-latest
    timeout-minutes: 30
    {%- if job_data == "generate-workflows" %}
    if: |
      !contains(github.event.pull_request.labels.*.name, 'Skip generate-workflows')
      && github.event.pull_request.user.login != 'opentelemetrybot' && github.event_name == 'pull_request'
    {%- endif %}
    {%- if job_data == "public-symbols-check" %}
    if: |
      !contains(github.event.pull_request.labels.*.name, 'Approve Public API check')
      && github.actor != 'opentelemetrybot' && github.event_name == 'pull_request'
    {%- endif %}
    {%- if job_data == "docs" %}
    if: |
      github.event.pull_request.user.login != 'opentelemetrybot' && github.event_name == 'pull_request'
    {%- endif %}
    steps:
      - name: Checkout repo @ SHA - ${% raw %}{{ github.sha }}{% endraw %}
        uses: actions/checkout@v4
      {%- if job_data == "public-symbols-check" %}
        with:
          fetch-depth: 0

      - name: Checkout main
        run: git checkout main

      - name: Pull origin
        run: git pull --rebase=false origin main

      - name: Checkout pull request
        run: git checkout ${% raw %}{{ github.event.pull_request.head.sha }}{% endraw %}
      {%- endif %}

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e {{ job_data }}
      {%- if job_data == "generate-workflows" %}

      - name: Check github workflows are up to date
        run: git diff --exit-code || (echo 'Generated workflows are out of date, run "tox -e generate-workflows" and commit the changes in this PR.' && exit 1)
      {%- endif %}
  {%- endfor %}
