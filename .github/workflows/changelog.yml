# This action requires that any PR targeting the main branch should touch at
# least one CHANGELOG file. If a CHANGELOG entry is not required, add the "Skip
# Changelog" label to disable this action.

name: changelog

on:
  pull_request:
    types: [opened, synchronize, reopened, labeled, unlabeled]
    branches:
      - main

jobs:
  changelog:
    runs-on: ubuntu-latest
    if: |
      !contains(github.event.pull_request.labels.*.name, 'Skip Changelog')
      && github.actor != 'opentelemetrybot'

    steps:
      - uses: actions/checkout@v4

      - name: Check for CHANGELOG changes
        run: |
          # Only the latest commit of the feature branch is available
          # automatically. To diff with the base branch, we need to
          # fetch that too (and we only need its latest commit).
          git fetch origin ${{ github.base_ref }} --depth=1
          if [[ $(git diff --name-only FETCH_HEAD | grep CHANGELOG) ]]
          then
            echo "A CHANGELOG was modified. Looks good!"
          else
            echo "No CHANGELOG was modified."
            echo "Please add a CHANGELOG entry, or add the \"Skip Changelog\" label if not required."
            false
          fi
