name: check-links
on:
  push:
    branches: [ main ]
  pull_request:

jobs:
  changedfiles:
    name: changed files
    runs-on: ubuntu-latest
    if: ${{ github.actor != 'dependabot[bot]' }}
    outputs:
      md: ${{ steps.changes.outputs.md }}
    steps:
      - name: Checkout Rep<PERSON>
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get changed files
        id: changes
        run: |
          echo "md=$(git diff --name-only --diff-filter=ACMRTUXB $(git merge-base origin/main ${{ github.event.pull_request.head.sha }}) ${{ github.event.pull_request.head.sha }} | grep .md$ | xargs)" >> $GITHUB_OUTPUT
  check-links:
    runs-on: ubuntu-latest
    needs: changedfiles
    if: |
      github.event.pull_request.user.login != 'opentelemetrybot' && github.event_name == 'pull_request'
      && ${{needs.changedfiles.outputs.md}}
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install markdown-link-check
        run: npm install -g markdown-link-check@v3.12.2

      - name: Run markdown-link-check
        run: |
          markdown-link-check \
            --verbose \
            --config .github/workflows/check_links_config.json \
            ${{needs.changedfiles.outputs.md}} \
            || { echo "Check that anchor links are lowercase"; exit 1; }
