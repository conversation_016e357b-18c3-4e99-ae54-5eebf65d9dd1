# Do not edit this file.
# This file is generated automatically by executing tox -e generate-workflows

name: Test 0

on:
  push:
    branches-ignore:
    - 'release/*'
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  CORE_REPO_SHA: main
  CONTRIB_REPO_SHA: main
  PIP_EXISTS_ACTION: w

jobs:

  py38-test-opentelemetry-api_ubuntu-latest:
    name: opentelemetry-api 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-api -- -ra

  py39-test-opentelemetry-api_ubuntu-latest:
    name: opentelemetry-api 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-api -- -ra

  py310-test-opentelemetry-api_ubuntu-latest:
    name: opentelemetry-api 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-api -- -ra

  py311-test-opentelemetry-api_ubuntu-latest:
    name: opentelemetry-api 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-api -- -ra

  py312-test-opentelemetry-api_ubuntu-latest:
    name: opentelemetry-api 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-api -- -ra

  py313-test-opentelemetry-api_ubuntu-latest:
    name: opentelemetry-api 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-api -- -ra

  pypy3-test-opentelemetry-api_ubuntu-latest:
    name: opentelemetry-api pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-api -- -ra

  py38-test-opentelemetry-proto-protobuf5_ubuntu-latest:
    name: opentelemetry-proto-protobuf5 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-proto-protobuf5 -- -ra

  py39-test-opentelemetry-proto-protobuf5_ubuntu-latest:
    name: opentelemetry-proto-protobuf5 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-proto-protobuf5 -- -ra

  py310-test-opentelemetry-proto-protobuf5_ubuntu-latest:
    name: opentelemetry-proto-protobuf5 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-proto-protobuf5 -- -ra

  py311-test-opentelemetry-proto-protobuf5_ubuntu-latest:
    name: opentelemetry-proto-protobuf5 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-proto-protobuf5 -- -ra

  py312-test-opentelemetry-proto-protobuf5_ubuntu-latest:
    name: opentelemetry-proto-protobuf5 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-proto-protobuf5 -- -ra

  py313-test-opentelemetry-proto-protobuf5_ubuntu-latest:
    name: opentelemetry-proto-protobuf5 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-proto-protobuf5 -- -ra

  pypy3-test-opentelemetry-proto-protobuf5_ubuntu-latest:
    name: opentelemetry-proto-protobuf5 pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-proto-protobuf5 -- -ra

  py38-test-opentelemetry-sdk_ubuntu-latest:
    name: opentelemetry-sdk 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-sdk -- -ra

  py39-test-opentelemetry-sdk_ubuntu-latest:
    name: opentelemetry-sdk 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-sdk -- -ra

  py310-test-opentelemetry-sdk_ubuntu-latest:
    name: opentelemetry-sdk 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-sdk -- -ra

  py311-test-opentelemetry-sdk_ubuntu-latest:
    name: opentelemetry-sdk 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-sdk -- -ra

  py312-test-opentelemetry-sdk_ubuntu-latest:
    name: opentelemetry-sdk 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-sdk -- -ra

  py313-test-opentelemetry-sdk_ubuntu-latest:
    name: opentelemetry-sdk 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-sdk -- -ra

  pypy3-test-opentelemetry-sdk_ubuntu-latest:
    name: opentelemetry-sdk pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-sdk -- -ra

  py38-test-opentelemetry-semantic-conventions_ubuntu-latest:
    name: opentelemetry-semantic-conventions 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-semantic-conventions -- -ra

  py39-test-opentelemetry-semantic-conventions_ubuntu-latest:
    name: opentelemetry-semantic-conventions 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-semantic-conventions -- -ra

  py310-test-opentelemetry-semantic-conventions_ubuntu-latest:
    name: opentelemetry-semantic-conventions 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-semantic-conventions -- -ra

  py311-test-opentelemetry-semantic-conventions_ubuntu-latest:
    name: opentelemetry-semantic-conventions 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-semantic-conventions -- -ra

  py312-test-opentelemetry-semantic-conventions_ubuntu-latest:
    name: opentelemetry-semantic-conventions 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-semantic-conventions -- -ra

  py313-test-opentelemetry-semantic-conventions_ubuntu-latest:
    name: opentelemetry-semantic-conventions 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-semantic-conventions -- -ra

  pypy3-test-opentelemetry-semantic-conventions_ubuntu-latest:
    name: opentelemetry-semantic-conventions pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-semantic-conventions -- -ra

  py38-test-opentelemetry-getting-started_ubuntu-latest:
    name: opentelemetry-getting-started 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-getting-started -- -ra

  py39-test-opentelemetry-getting-started_ubuntu-latest:
    name: opentelemetry-getting-started 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-getting-started -- -ra

  py310-test-opentelemetry-getting-started_ubuntu-latest:
    name: opentelemetry-getting-started 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-getting-started -- -ra

  py311-test-opentelemetry-getting-started_ubuntu-latest:
    name: opentelemetry-getting-started 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-getting-started -- -ra

  py312-test-opentelemetry-getting-started_ubuntu-latest:
    name: opentelemetry-getting-started 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-getting-started -- -ra

  py313-test-opentelemetry-getting-started_ubuntu-latest:
    name: opentelemetry-getting-started 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-getting-started -- -ra

  py38-test-opentelemetry-opentracing-shim_ubuntu-latest:
    name: opentelemetry-opentracing-shim 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-opentracing-shim -- -ra

  py39-test-opentelemetry-opentracing-shim_ubuntu-latest:
    name: opentelemetry-opentracing-shim 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-opentracing-shim -- -ra

  py310-test-opentelemetry-opentracing-shim_ubuntu-latest:
    name: opentelemetry-opentracing-shim 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-opentracing-shim -- -ra

  py311-test-opentelemetry-opentracing-shim_ubuntu-latest:
    name: opentelemetry-opentracing-shim 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-opentracing-shim -- -ra

  py312-test-opentelemetry-opentracing-shim_ubuntu-latest:
    name: opentelemetry-opentracing-shim 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-opentracing-shim -- -ra

  py313-test-opentelemetry-opentracing-shim_ubuntu-latest:
    name: opentelemetry-opentracing-shim 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-opentracing-shim -- -ra

  pypy3-test-opentelemetry-opentracing-shim_ubuntu-latest:
    name: opentelemetry-opentracing-shim pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-opentracing-shim -- -ra

  py38-test-opentelemetry-opencensus-shim_ubuntu-latest:
    name: opentelemetry-opencensus-shim 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-opencensus-shim -- -ra

  py39-test-opentelemetry-opencensus-shim_ubuntu-latest:
    name: opentelemetry-opencensus-shim 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-opencensus-shim -- -ra

  py310-test-opentelemetry-opencensus-shim_ubuntu-latest:
    name: opentelemetry-opencensus-shim 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-opencensus-shim -- -ra

  py311-test-opentelemetry-opencensus-shim_ubuntu-latest:
    name: opentelemetry-opencensus-shim 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-opencensus-shim -- -ra

  py312-test-opentelemetry-opencensus-shim_ubuntu-latest:
    name: opentelemetry-opencensus-shim 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-opencensus-shim -- -ra

  py313-test-opentelemetry-opencensus-shim_ubuntu-latest:
    name: opentelemetry-opencensus-shim 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-opencensus-shim -- -ra

  py38-test-opentelemetry-exporter-opencensus_ubuntu-latest:
    name: opentelemetry-exporter-opencensus 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-opencensus -- -ra

  py39-test-opentelemetry-exporter-opencensus_ubuntu-latest:
    name: opentelemetry-exporter-opencensus 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-opencensus -- -ra

  py310-test-opentelemetry-exporter-opencensus_ubuntu-latest:
    name: opentelemetry-exporter-opencensus 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-opencensus -- -ra

  py311-test-opentelemetry-exporter-opencensus_ubuntu-latest:
    name: opentelemetry-exporter-opencensus 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-opencensus -- -ra

  py312-test-opentelemetry-exporter-opencensus_ubuntu-latest:
    name: opentelemetry-exporter-opencensus 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-opencensus -- -ra

  py313-test-opentelemetry-exporter-opencensus_ubuntu-latest:
    name: opentelemetry-exporter-opencensus 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-opencensus -- -ra

  py38-test-opentelemetry-exporter-otlp-proto-common_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py39-test-opentelemetry-exporter-otlp-proto-common_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py310-test-opentelemetry-exporter-otlp-proto-common_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py311-test-opentelemetry-exporter-otlp-proto-common_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py312-test-opentelemetry-exporter-otlp-proto-common_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py313-test-opentelemetry-exporter-otlp-proto-common_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-otlp-proto-common -- -ra

  pypy3-test-opentelemetry-exporter-otlp-proto-common_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-common pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py38-test-opentelemetry-exporter-otlp-combined_ubuntu-latest:
    name: opentelemetry-exporter-otlp-combined 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-otlp-combined -- -ra

  py39-test-opentelemetry-exporter-otlp-combined_ubuntu-latest:
    name: opentelemetry-exporter-otlp-combined 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-otlp-combined -- -ra

  py310-test-opentelemetry-exporter-otlp-combined_ubuntu-latest:
    name: opentelemetry-exporter-otlp-combined 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-otlp-combined -- -ra

  py311-test-opentelemetry-exporter-otlp-combined_ubuntu-latest:
    name: opentelemetry-exporter-otlp-combined 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-otlp-combined -- -ra

  py312-test-opentelemetry-exporter-otlp-combined_ubuntu-latest:
    name: opentelemetry-exporter-otlp-combined 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-otlp-combined -- -ra

  py313-test-opentelemetry-exporter-otlp-combined_ubuntu-latest:
    name: opentelemetry-exporter-otlp-combined 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-otlp-combined -- -ra

  py38-test-opentelemetry-exporter-otlp-proto-grpc_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py39-test-opentelemetry-exporter-otlp-proto-grpc_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py310-test-opentelemetry-exporter-otlp-proto-grpc_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py311-test-opentelemetry-exporter-otlp-proto-grpc_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py312-test-opentelemetry-exporter-otlp-proto-grpc_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py313-test-opentelemetry-exporter-otlp-proto-grpc_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py38-test-opentelemetry-exporter-otlp-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py39-test-opentelemetry-exporter-otlp-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py310-test-opentelemetry-exporter-otlp-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py311-test-opentelemetry-exporter-otlp-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py312-test-opentelemetry-exporter-otlp-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py313-test-opentelemetry-exporter-otlp-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-otlp-proto-http -- -ra

  pypy3-test-opentelemetry-exporter-otlp-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-otlp-proto-http pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py38-test-opentelemetry-exporter-prometheus_ubuntu-latest:
    name: opentelemetry-exporter-prometheus 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-prometheus -- -ra

  py39-test-opentelemetry-exporter-prometheus_ubuntu-latest:
    name: opentelemetry-exporter-prometheus 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-prometheus -- -ra

  py310-test-opentelemetry-exporter-prometheus_ubuntu-latest:
    name: opentelemetry-exporter-prometheus 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-prometheus -- -ra

  py311-test-opentelemetry-exporter-prometheus_ubuntu-latest:
    name: opentelemetry-exporter-prometheus 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-prometheus -- -ra

  py312-test-opentelemetry-exporter-prometheus_ubuntu-latest:
    name: opentelemetry-exporter-prometheus 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-prometheus -- -ra

  py313-test-opentelemetry-exporter-prometheus_ubuntu-latest:
    name: opentelemetry-exporter-prometheus 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-prometheus -- -ra

  pypy3-test-opentelemetry-exporter-prometheus_ubuntu-latest:
    name: opentelemetry-exporter-prometheus pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-prometheus -- -ra

  py38-test-opentelemetry-exporter-zipkin-combined_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-combined 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-zipkin-combined -- -ra

  py39-test-opentelemetry-exporter-zipkin-combined_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-combined 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-zipkin-combined -- -ra

  py310-test-opentelemetry-exporter-zipkin-combined_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-combined 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-zipkin-combined -- -ra

  py311-test-opentelemetry-exporter-zipkin-combined_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-combined 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-zipkin-combined -- -ra

  py312-test-opentelemetry-exporter-zipkin-combined_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-combined 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-zipkin-combined -- -ra

  py313-test-opentelemetry-exporter-zipkin-combined_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-combined 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-zipkin-combined -- -ra

  pypy3-test-opentelemetry-exporter-zipkin-combined_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-combined pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-zipkin-combined -- -ra

  py38-test-opentelemetry-exporter-zipkin-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py39-test-opentelemetry-exporter-zipkin-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py310-test-opentelemetry-exporter-zipkin-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py311-test-opentelemetry-exporter-zipkin-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py312-test-opentelemetry-exporter-zipkin-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py313-test-opentelemetry-exporter-zipkin-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  pypy3-test-opentelemetry-exporter-zipkin-proto-http_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-proto-http pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py38-test-opentelemetry-exporter-zipkin-json_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-json 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-zipkin-json -- -ra

  py39-test-opentelemetry-exporter-zipkin-json_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-json 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-zipkin-json -- -ra

  py310-test-opentelemetry-exporter-zipkin-json_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-json 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-zipkin-json -- -ra

  py311-test-opentelemetry-exporter-zipkin-json_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-json 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-zipkin-json -- -ra

  py312-test-opentelemetry-exporter-zipkin-json_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-json 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-zipkin-json -- -ra

  py313-test-opentelemetry-exporter-zipkin-json_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-json 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-zipkin-json -- -ra

  pypy3-test-opentelemetry-exporter-zipkin-json_ubuntu-latest:
    name: opentelemetry-exporter-zipkin-json pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-zipkin-json -- -ra

  py38-test-opentelemetry-propagator-b3_ubuntu-latest:
    name: opentelemetry-propagator-b3 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-propagator-b3 -- -ra

  py39-test-opentelemetry-propagator-b3_ubuntu-latest:
    name: opentelemetry-propagator-b3 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-propagator-b3 -- -ra

  py310-test-opentelemetry-propagator-b3_ubuntu-latest:
    name: opentelemetry-propagator-b3 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-propagator-b3 -- -ra

  py311-test-opentelemetry-propagator-b3_ubuntu-latest:
    name: opentelemetry-propagator-b3 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-propagator-b3 -- -ra

  py312-test-opentelemetry-propagator-b3_ubuntu-latest:
    name: opentelemetry-propagator-b3 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-propagator-b3 -- -ra

  py313-test-opentelemetry-propagator-b3_ubuntu-latest:
    name: opentelemetry-propagator-b3 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-propagator-b3 -- -ra

  pypy3-test-opentelemetry-propagator-b3_ubuntu-latest:
    name: opentelemetry-propagator-b3 pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-propagator-b3 -- -ra

  py38-test-opentelemetry-propagator-jaeger_ubuntu-latest:
    name: opentelemetry-propagator-jaeger 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-propagator-jaeger -- -ra

  py39-test-opentelemetry-propagator-jaeger_ubuntu-latest:
    name: opentelemetry-propagator-jaeger 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-propagator-jaeger -- -ra

  py310-test-opentelemetry-propagator-jaeger_ubuntu-latest:
    name: opentelemetry-propagator-jaeger 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-propagator-jaeger -- -ra

  py311-test-opentelemetry-propagator-jaeger_ubuntu-latest:
    name: opentelemetry-propagator-jaeger 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-propagator-jaeger -- -ra

  py312-test-opentelemetry-propagator-jaeger_ubuntu-latest:
    name: opentelemetry-propagator-jaeger 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-propagator-jaeger -- -ra

  py313-test-opentelemetry-propagator-jaeger_ubuntu-latest:
    name: opentelemetry-propagator-jaeger 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-propagator-jaeger -- -ra

  pypy3-test-opentelemetry-propagator-jaeger_ubuntu-latest:
    name: opentelemetry-propagator-jaeger pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-propagator-jaeger -- -ra

  py38-test-opentelemetry-test-utils_ubuntu-latest:
    name: opentelemetry-test-utils 3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py38-test-opentelemetry-test-utils -- -ra

  py39-test-opentelemetry-test-utils_ubuntu-latest:
    name: opentelemetry-test-utils 3.9 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py39-test-opentelemetry-test-utils -- -ra

  py310-test-opentelemetry-test-utils_ubuntu-latest:
    name: opentelemetry-test-utils 3.10 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py310-test-opentelemetry-test-utils -- -ra

  py311-test-opentelemetry-test-utils_ubuntu-latest:
    name: opentelemetry-test-utils 3.11 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py311-test-opentelemetry-test-utils -- -ra

  py312-test-opentelemetry-test-utils_ubuntu-latest:
    name: opentelemetry-test-utils 3.12 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py312-test-opentelemetry-test-utils -- -ra

  py313-test-opentelemetry-test-utils_ubuntu-latest:
    name: opentelemetry-test-utils 3.13 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e py313-test-opentelemetry-test-utils -- -ra

  pypy3-test-opentelemetry-test-utils_ubuntu-latest:
    name: opentelemetry-test-utils pypy-3.8 Ubuntu
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-test-utils -- -ra

  py38-test-opentelemetry-api_windows-latest:
    name: opentelemetry-api 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-api -- -ra

  py39-test-opentelemetry-api_windows-latest:
    name: opentelemetry-api 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-api -- -ra

  py310-test-opentelemetry-api_windows-latest:
    name: opentelemetry-api 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-api -- -ra

  py311-test-opentelemetry-api_windows-latest:
    name: opentelemetry-api 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-api -- -ra

  py312-test-opentelemetry-api_windows-latest:
    name: opentelemetry-api 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-api -- -ra

  py313-test-opentelemetry-api_windows-latest:
    name: opentelemetry-api 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-api -- -ra

  pypy3-test-opentelemetry-api_windows-latest:
    name: opentelemetry-api pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-api -- -ra

  py38-test-opentelemetry-proto-protobuf5_windows-latest:
    name: opentelemetry-proto-protobuf5 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-proto-protobuf5 -- -ra

  py39-test-opentelemetry-proto-protobuf5_windows-latest:
    name: opentelemetry-proto-protobuf5 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-proto-protobuf5 -- -ra

  py310-test-opentelemetry-proto-protobuf5_windows-latest:
    name: opentelemetry-proto-protobuf5 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-proto-protobuf5 -- -ra

  py311-test-opentelemetry-proto-protobuf5_windows-latest:
    name: opentelemetry-proto-protobuf5 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-proto-protobuf5 -- -ra

  py312-test-opentelemetry-proto-protobuf5_windows-latest:
    name: opentelemetry-proto-protobuf5 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-proto-protobuf5 -- -ra

  py313-test-opentelemetry-proto-protobuf5_windows-latest:
    name: opentelemetry-proto-protobuf5 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-proto-protobuf5 -- -ra

  pypy3-test-opentelemetry-proto-protobuf5_windows-latest:
    name: opentelemetry-proto-protobuf5 pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-proto-protobuf5 -- -ra

  py38-test-opentelemetry-sdk_windows-latest:
    name: opentelemetry-sdk 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-sdk -- -ra

  py39-test-opentelemetry-sdk_windows-latest:
    name: opentelemetry-sdk 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-sdk -- -ra

  py310-test-opentelemetry-sdk_windows-latest:
    name: opentelemetry-sdk 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-sdk -- -ra

  py311-test-opentelemetry-sdk_windows-latest:
    name: opentelemetry-sdk 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-sdk -- -ra

  py312-test-opentelemetry-sdk_windows-latest:
    name: opentelemetry-sdk 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-sdk -- -ra

  py313-test-opentelemetry-sdk_windows-latest:
    name: opentelemetry-sdk 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-sdk -- -ra

  pypy3-test-opentelemetry-sdk_windows-latest:
    name: opentelemetry-sdk pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-sdk -- -ra

  py38-test-opentelemetry-semantic-conventions_windows-latest:
    name: opentelemetry-semantic-conventions 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-semantic-conventions -- -ra

  py39-test-opentelemetry-semantic-conventions_windows-latest:
    name: opentelemetry-semantic-conventions 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-semantic-conventions -- -ra

  py310-test-opentelemetry-semantic-conventions_windows-latest:
    name: opentelemetry-semantic-conventions 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-semantic-conventions -- -ra

  py311-test-opentelemetry-semantic-conventions_windows-latest:
    name: opentelemetry-semantic-conventions 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-semantic-conventions -- -ra

  py312-test-opentelemetry-semantic-conventions_windows-latest:
    name: opentelemetry-semantic-conventions 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-semantic-conventions -- -ra

  py313-test-opentelemetry-semantic-conventions_windows-latest:
    name: opentelemetry-semantic-conventions 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-semantic-conventions -- -ra

  pypy3-test-opentelemetry-semantic-conventions_windows-latest:
    name: opentelemetry-semantic-conventions pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-semantic-conventions -- -ra

  py38-test-opentelemetry-getting-started_windows-latest:
    name: opentelemetry-getting-started 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-getting-started -- -ra

  py39-test-opentelemetry-getting-started_windows-latest:
    name: opentelemetry-getting-started 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-getting-started -- -ra

  py310-test-opentelemetry-getting-started_windows-latest:
    name: opentelemetry-getting-started 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-getting-started -- -ra

  py311-test-opentelemetry-getting-started_windows-latest:
    name: opentelemetry-getting-started 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-getting-started -- -ra

  py312-test-opentelemetry-getting-started_windows-latest:
    name: opentelemetry-getting-started 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-getting-started -- -ra

  py313-test-opentelemetry-getting-started_windows-latest:
    name: opentelemetry-getting-started 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-getting-started -- -ra

  py38-test-opentelemetry-opentracing-shim_windows-latest:
    name: opentelemetry-opentracing-shim 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-opentracing-shim -- -ra

  py39-test-opentelemetry-opentracing-shim_windows-latest:
    name: opentelemetry-opentracing-shim 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-opentracing-shim -- -ra

  py310-test-opentelemetry-opentracing-shim_windows-latest:
    name: opentelemetry-opentracing-shim 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-opentracing-shim -- -ra

  py311-test-opentelemetry-opentracing-shim_windows-latest:
    name: opentelemetry-opentracing-shim 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-opentracing-shim -- -ra

  py312-test-opentelemetry-opentracing-shim_windows-latest:
    name: opentelemetry-opentracing-shim 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-opentracing-shim -- -ra

  py313-test-opentelemetry-opentracing-shim_windows-latest:
    name: opentelemetry-opentracing-shim 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-opentracing-shim -- -ra

  pypy3-test-opentelemetry-opentracing-shim_windows-latest:
    name: opentelemetry-opentracing-shim pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-opentracing-shim -- -ra

  py38-test-opentelemetry-opencensus-shim_windows-latest:
    name: opentelemetry-opencensus-shim 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-opencensus-shim -- -ra

  py39-test-opentelemetry-opencensus-shim_windows-latest:
    name: opentelemetry-opencensus-shim 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-opencensus-shim -- -ra

  py310-test-opentelemetry-opencensus-shim_windows-latest:
    name: opentelemetry-opencensus-shim 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-opencensus-shim -- -ra

  py311-test-opentelemetry-opencensus-shim_windows-latest:
    name: opentelemetry-opencensus-shim 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-opencensus-shim -- -ra

  py312-test-opentelemetry-opencensus-shim_windows-latest:
    name: opentelemetry-opencensus-shim 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-opencensus-shim -- -ra

  py313-test-opentelemetry-opencensus-shim_windows-latest:
    name: opentelemetry-opencensus-shim 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-opencensus-shim -- -ra

  py38-test-opentelemetry-exporter-opencensus_windows-latest:
    name: opentelemetry-exporter-opencensus 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-opencensus -- -ra

  py39-test-opentelemetry-exporter-opencensus_windows-latest:
    name: opentelemetry-exporter-opencensus 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-opencensus -- -ra

  py310-test-opentelemetry-exporter-opencensus_windows-latest:
    name: opentelemetry-exporter-opencensus 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-opencensus -- -ra

  py311-test-opentelemetry-exporter-opencensus_windows-latest:
    name: opentelemetry-exporter-opencensus 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-opencensus -- -ra

  py312-test-opentelemetry-exporter-opencensus_windows-latest:
    name: opentelemetry-exporter-opencensus 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-opencensus -- -ra

  py313-test-opentelemetry-exporter-opencensus_windows-latest:
    name: opentelemetry-exporter-opencensus 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-opencensus -- -ra

  py38-test-opentelemetry-exporter-otlp-proto-common_windows-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py39-test-opentelemetry-exporter-otlp-proto-common_windows-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py310-test-opentelemetry-exporter-otlp-proto-common_windows-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py311-test-opentelemetry-exporter-otlp-proto-common_windows-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py312-test-opentelemetry-exporter-otlp-proto-common_windows-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py313-test-opentelemetry-exporter-otlp-proto-common_windows-latest:
    name: opentelemetry-exporter-otlp-proto-common 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-otlp-proto-common -- -ra

  pypy3-test-opentelemetry-exporter-otlp-proto-common_windows-latest:
    name: opentelemetry-exporter-otlp-proto-common pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-otlp-proto-common -- -ra

  py38-test-opentelemetry-exporter-otlp-combined_windows-latest:
    name: opentelemetry-exporter-otlp-combined 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-otlp-combined -- -ra

  py39-test-opentelemetry-exporter-otlp-combined_windows-latest:
    name: opentelemetry-exporter-otlp-combined 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-otlp-combined -- -ra

  py310-test-opentelemetry-exporter-otlp-combined_windows-latest:
    name: opentelemetry-exporter-otlp-combined 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-otlp-combined -- -ra

  py311-test-opentelemetry-exporter-otlp-combined_windows-latest:
    name: opentelemetry-exporter-otlp-combined 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-otlp-combined -- -ra

  py312-test-opentelemetry-exporter-otlp-combined_windows-latest:
    name: opentelemetry-exporter-otlp-combined 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-otlp-combined -- -ra

  py313-test-opentelemetry-exporter-otlp-combined_windows-latest:
    name: opentelemetry-exporter-otlp-combined 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-otlp-combined -- -ra

  py38-test-opentelemetry-exporter-otlp-proto-grpc_windows-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py39-test-opentelemetry-exporter-otlp-proto-grpc_windows-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py310-test-opentelemetry-exporter-otlp-proto-grpc_windows-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py311-test-opentelemetry-exporter-otlp-proto-grpc_windows-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py312-test-opentelemetry-exporter-otlp-proto-grpc_windows-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py313-test-opentelemetry-exporter-otlp-proto-grpc_windows-latest:
    name: opentelemetry-exporter-otlp-proto-grpc 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-otlp-proto-grpc -- -ra

  py38-test-opentelemetry-exporter-otlp-proto-http_windows-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py39-test-opentelemetry-exporter-otlp-proto-http_windows-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py310-test-opentelemetry-exporter-otlp-proto-http_windows-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py311-test-opentelemetry-exporter-otlp-proto-http_windows-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py312-test-opentelemetry-exporter-otlp-proto-http_windows-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py313-test-opentelemetry-exporter-otlp-proto-http_windows-latest:
    name: opentelemetry-exporter-otlp-proto-http 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-otlp-proto-http -- -ra

  pypy3-test-opentelemetry-exporter-otlp-proto-http_windows-latest:
    name: opentelemetry-exporter-otlp-proto-http pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-otlp-proto-http -- -ra

  py38-test-opentelemetry-exporter-prometheus_windows-latest:
    name: opentelemetry-exporter-prometheus 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-prometheus -- -ra

  py39-test-opentelemetry-exporter-prometheus_windows-latest:
    name: opentelemetry-exporter-prometheus 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-prometheus -- -ra

  py310-test-opentelemetry-exporter-prometheus_windows-latest:
    name: opentelemetry-exporter-prometheus 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-prometheus -- -ra

  py311-test-opentelemetry-exporter-prometheus_windows-latest:
    name: opentelemetry-exporter-prometheus 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-prometheus -- -ra

  py312-test-opentelemetry-exporter-prometheus_windows-latest:
    name: opentelemetry-exporter-prometheus 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-prometheus -- -ra

  py313-test-opentelemetry-exporter-prometheus_windows-latest:
    name: opentelemetry-exporter-prometheus 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-prometheus -- -ra

  pypy3-test-opentelemetry-exporter-prometheus_windows-latest:
    name: opentelemetry-exporter-prometheus pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-prometheus -- -ra

  py38-test-opentelemetry-exporter-zipkin-combined_windows-latest:
    name: opentelemetry-exporter-zipkin-combined 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-zipkin-combined -- -ra

  py39-test-opentelemetry-exporter-zipkin-combined_windows-latest:
    name: opentelemetry-exporter-zipkin-combined 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-zipkin-combined -- -ra

  py310-test-opentelemetry-exporter-zipkin-combined_windows-latest:
    name: opentelemetry-exporter-zipkin-combined 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-zipkin-combined -- -ra

  py311-test-opentelemetry-exporter-zipkin-combined_windows-latest:
    name: opentelemetry-exporter-zipkin-combined 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-zipkin-combined -- -ra

  py312-test-opentelemetry-exporter-zipkin-combined_windows-latest:
    name: opentelemetry-exporter-zipkin-combined 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-zipkin-combined -- -ra

  py313-test-opentelemetry-exporter-zipkin-combined_windows-latest:
    name: opentelemetry-exporter-zipkin-combined 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-zipkin-combined -- -ra

  pypy3-test-opentelemetry-exporter-zipkin-combined_windows-latest:
    name: opentelemetry-exporter-zipkin-combined pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-zipkin-combined -- -ra

  py38-test-opentelemetry-exporter-zipkin-proto-http_windows-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py39-test-opentelemetry-exporter-zipkin-proto-http_windows-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py310-test-opentelemetry-exporter-zipkin-proto-http_windows-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py311-test-opentelemetry-exporter-zipkin-proto-http_windows-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py312-test-opentelemetry-exporter-zipkin-proto-http_windows-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py313-test-opentelemetry-exporter-zipkin-proto-http_windows-latest:
    name: opentelemetry-exporter-zipkin-proto-http 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  pypy3-test-opentelemetry-exporter-zipkin-proto-http_windows-latest:
    name: opentelemetry-exporter-zipkin-proto-http pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-zipkin-proto-http -- -ra

  py38-test-opentelemetry-exporter-zipkin-json_windows-latest:
    name: opentelemetry-exporter-zipkin-json 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-exporter-zipkin-json -- -ra

  py39-test-opentelemetry-exporter-zipkin-json_windows-latest:
    name: opentelemetry-exporter-zipkin-json 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-exporter-zipkin-json -- -ra

  py310-test-opentelemetry-exporter-zipkin-json_windows-latest:
    name: opentelemetry-exporter-zipkin-json 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-exporter-zipkin-json -- -ra

  py311-test-opentelemetry-exporter-zipkin-json_windows-latest:
    name: opentelemetry-exporter-zipkin-json 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-exporter-zipkin-json -- -ra

  py312-test-opentelemetry-exporter-zipkin-json_windows-latest:
    name: opentelemetry-exporter-zipkin-json 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-exporter-zipkin-json -- -ra

  py313-test-opentelemetry-exporter-zipkin-json_windows-latest:
    name: opentelemetry-exporter-zipkin-json 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-exporter-zipkin-json -- -ra

  pypy3-test-opentelemetry-exporter-zipkin-json_windows-latest:
    name: opentelemetry-exporter-zipkin-json pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-exporter-zipkin-json -- -ra

  py38-test-opentelemetry-propagator-b3_windows-latest:
    name: opentelemetry-propagator-b3 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-propagator-b3 -- -ra

  py39-test-opentelemetry-propagator-b3_windows-latest:
    name: opentelemetry-propagator-b3 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-propagator-b3 -- -ra

  py310-test-opentelemetry-propagator-b3_windows-latest:
    name: opentelemetry-propagator-b3 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-propagator-b3 -- -ra

  py311-test-opentelemetry-propagator-b3_windows-latest:
    name: opentelemetry-propagator-b3 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-propagator-b3 -- -ra

  py312-test-opentelemetry-propagator-b3_windows-latest:
    name: opentelemetry-propagator-b3 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-propagator-b3 -- -ra

  py313-test-opentelemetry-propagator-b3_windows-latest:
    name: opentelemetry-propagator-b3 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-propagator-b3 -- -ra

  pypy3-test-opentelemetry-propagator-b3_windows-latest:
    name: opentelemetry-propagator-b3 pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-propagator-b3 -- -ra

  py38-test-opentelemetry-propagator-jaeger_windows-latest:
    name: opentelemetry-propagator-jaeger 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-propagator-jaeger -- -ra

  py39-test-opentelemetry-propagator-jaeger_windows-latest:
    name: opentelemetry-propagator-jaeger 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-propagator-jaeger -- -ra

  py310-test-opentelemetry-propagator-jaeger_windows-latest:
    name: opentelemetry-propagator-jaeger 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-propagator-jaeger -- -ra

  py311-test-opentelemetry-propagator-jaeger_windows-latest:
    name: opentelemetry-propagator-jaeger 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-propagator-jaeger -- -ra

  py312-test-opentelemetry-propagator-jaeger_windows-latest:
    name: opentelemetry-propagator-jaeger 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-propagator-jaeger -- -ra

  py313-test-opentelemetry-propagator-jaeger_windows-latest:
    name: opentelemetry-propagator-jaeger 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-propagator-jaeger -- -ra

  pypy3-test-opentelemetry-propagator-jaeger_windows-latest:
    name: opentelemetry-propagator-jaeger pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-propagator-jaeger -- -ra

  py38-test-opentelemetry-test-utils_windows-latest:
    name: opentelemetry-test-utils 3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py38-test-opentelemetry-test-utils -- -ra
