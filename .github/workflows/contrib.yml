name: Core Contrib Test

on:
  push:
    branches-ignore:
    - 'release/*'
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  contrib_0:
    uses: open-telemetry/opentelemetry-python-contrib/.github/workflows/core_contrib_test_0.yml@main
    with:
      CORE_REPO_SHA: ${{ github.sha }}
      CONTRIB_REPO_SHA: main
