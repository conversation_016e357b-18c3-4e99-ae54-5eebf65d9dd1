# Do not edit this file.
# This file is generated automatically by executing tox -e generate-workflows

name: Lint 0

on:
  push:
    branches-ignore:
    - 'release/*'
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  CORE_REPO_SHA: main
  CONTRIB_REPO_SHA: main
  PIP_EXISTS_ACTION: w

jobs:

  lint-opentelemetry-api:
    name: opentelemetry-api
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-api

  lint-opentelemetry-proto-protobuf5:
    name: opentelemetry-proto-protobuf5
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-proto-protobuf5

  lint-opentelemetry-sdk:
    name: opentelemetry-sdk
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-sdk

  lint-opentelemetry-semantic-conventions:
    name: opentelemetry-semantic-conventions
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-semantic-conventions

  lint-opentelemetry-getting-started:
    name: opentelemetry-getting-started
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-getting-started

  lint-opentelemetry-opentracing-shim:
    name: opentelemetry-opentracing-shim
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-opentracing-shim

  lint-opentelemetry-opencensus-shim:
    name: opentelemetry-opencensus-shim
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-opencensus-shim

  lint-opentelemetry-exporter-opencensus:
    name: opentelemetry-exporter-opencensus
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-exporter-opencensus

  lint-opentelemetry-exporter-otlp-proto-common:
    name: opentelemetry-exporter-otlp-proto-common
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-exporter-otlp-proto-common

  lint-opentelemetry-exporter-otlp-combined:
    name: opentelemetry-exporter-otlp-combined
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-exporter-otlp-combined

  lint-opentelemetry-exporter-otlp-proto-grpc:
    name: opentelemetry-exporter-otlp-proto-grpc
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-exporter-otlp-proto-grpc

  lint-opentelemetry-exporter-otlp-proto-http:
    name: opentelemetry-exporter-otlp-proto-http
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-exporter-otlp-proto-http

  lint-opentelemetry-exporter-prometheus:
    name: opentelemetry-exporter-prometheus
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-exporter-prometheus

  lint-opentelemetry-exporter-zipkin-combined:
    name: opentelemetry-exporter-zipkin-combined
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-exporter-zipkin-combined

  lint-opentelemetry-exporter-zipkin-proto-http:
    name: opentelemetry-exporter-zipkin-proto-http
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-exporter-zipkin-proto-http

  lint-opentelemetry-exporter-zipkin-json:
    name: opentelemetry-exporter-zipkin-json
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-exporter-zipkin-json

  lint-opentelemetry-propagator-b3:
    name: opentelemetry-propagator-b3
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-propagator-b3

  lint-opentelemetry-propagator-jaeger:
    name: opentelemetry-propagator-jaeger
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-propagator-jaeger

  lint-opentelemetry-test-utils:
    name: opentelemetry-test-utils
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Run tests
        run: tox -e lint-opentelemetry-test-utils
