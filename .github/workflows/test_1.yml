# Do not edit this file.
# This file is generated automatically by executing tox -e generate-workflows

name: Test 1

on:
  push:
    branches-ignore:
    - 'release/*'
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  CORE_REPO_SHA: main
  CONTRIB_REPO_SHA: main
  PIP_EXISTS_ACTION: w

jobs:

  py39-test-opentelemetry-test-utils_windows-latest:
    name: opentelemetry-test-utils 3.9 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py39-test-opentelemetry-test-utils -- -ra

  py310-test-opentelemetry-test-utils_windows-latest:
    name: opentelemetry-test-utils 3.10 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py310-test-opentelemetry-test-utils -- -ra

  py311-test-opentelemetry-test-utils_windows-latest:
    name: opentelemetry-test-utils 3.11 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py311-test-opentelemetry-test-utils -- -ra

  py312-test-opentelemetry-test-utils_windows-latest:
    name: opentelemetry-test-utils 3.12 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py312-test-opentelemetry-test-utils -- -ra

  py313-test-opentelemetry-test-utils_windows-latest:
    name: opentelemetry-test-utils 3.13 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e py313-test-opentelemetry-test-utils -- -ra

  pypy3-test-opentelemetry-test-utils_windows-latest:
    name: opentelemetry-test-utils pypy-3.8 Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v4

      - name: Set up Python pypy-3.8
        uses: actions/setup-python@v5
        with:
          python-version: "pypy-3.8"

      - name: Install tox
        run: pip install tox

      - name: Configure git to support long filenames
        run: git config --system core.longpaths true

      - name: Run tests
        run: tox -e pypy3-test-opentelemetry-test-utils -- -ra
