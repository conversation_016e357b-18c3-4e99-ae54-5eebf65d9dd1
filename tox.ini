[tox]
isolated_build = True
skipsdist = True
skip_missing_interpreters = True
envlist =
    ; Environments are organized by individual package, allowing
    ; for specifying supported Python versions per package.

    py3{8,9,10,11,12,13}-test-opentelemetry-api
    pypy3-test-opentelemetry-api
    lint-opentelemetry-api

    py3{8,9,10,11,12,13}-test-opentelemetry-proto-protobuf5
    pypy3-test-opentelemetry-proto-protobuf5
    lint-opentelemetry-proto-protobuf5

    py3{8,9,10,11,12,13}-test-opentelemetry-sdk
    pypy3-test-opentelemetry-sdk
    lint-opentelemetry-sdk
    benchmark-opentelemetry-sdk

    py3{8,9,10,11,12,13}-test-opentelemetry-semantic-conventions
    pypy3-test-opentelemetry-semantic-conventions
    lint-opentelemetry-semantic-conventions

    py3{8,9,10,11,12,13}-test-opentelemetry-getting-started
    lint-opentelemetry-getting-started

    py3{8,9,10,11,12,13}-test-opentelemetry-opentracing-shim
    pypy3-test-opentelemetry-opentracing-shim
    lint-opentelemetry-opentracing-shim

    py3{8,9,10,11,12,13}-test-opentelemetry-opencensus-shim
    ; opencensus-shim intentionally excluded from pypy3 (grpcio install fails)
    lint-opentelemetry-opencensus-shim

    py3{8,9,10,11,12,13}-test-opentelemetry-exporter-opencensus
    ; exporter-opencensus intentionally excluded from pypy3
    lint-opentelemetry-exporter-opencensus

    py3{8,9,10,11,12,13}-test-opentelemetry-exporter-otlp-proto-common
    pypy3-test-opentelemetry-exporter-otlp-proto-common
    lint-opentelemetry-exporter-otlp-proto-common

    ; opentelemetry-exporter-otlp
    py3{8,9,10,11,12,13}-test-opentelemetry-exporter-otlp-combined
    ; intentionally excluded from pypy3
    lint-opentelemetry-exporter-otlp-combined

    py3{8,9,10,11,12,13}-test-opentelemetry-exporter-otlp-proto-grpc
    ; intentionally excluded from pypy3
    lint-opentelemetry-exporter-otlp-proto-grpc
    benchmark-opentelemetry-exporter-otlp-proto-grpc

    py3{8,9,10,11,12,13}-test-opentelemetry-exporter-otlp-proto-http
    pypy3-test-opentelemetry-exporter-otlp-proto-http
    lint-opentelemetry-exporter-otlp-proto-http

    py3{8,9,10,11,12,13}-test-opentelemetry-exporter-prometheus
    pypy3-test-opentelemetry-exporter-prometheus
    lint-opentelemetry-exporter-prometheus

    ; opentelemetry-exporter-zipkin
    py3{8,9,10,11,12,13}-test-opentelemetry-exporter-zipkin-combined
    pypy3-test-opentelemetry-exporter-zipkin-combined
    lint-opentelemetry-exporter-zipkin-combined

    py3{8,9,10,11,12,13}-test-opentelemetry-exporter-zipkin-proto-http
    pypy3-test-opentelemetry-exporter-zipkin-proto-http
    lint-opentelemetry-exporter-zipkin-proto-http

    py3{8,9,10,11,12,13}-test-opentelemetry-exporter-zipkin-json
    pypy3-test-opentelemetry-exporter-zipkin-json
    lint-opentelemetry-exporter-zipkin-json

    py3{8,9,10,11,12,13}-test-opentelemetry-propagator-b3
    pypy3-test-opentelemetry-propagator-b3
    lint-opentelemetry-propagator-b3
    benchmark-opentelemetry-propagator-b3

    py3{8,9,10,11,12,13}-test-opentelemetry-propagator-jaeger
    pypy3-test-opentelemetry-propagator-jaeger
    lint-opentelemetry-propagator-jaeger

    py3{8,9,10,11,12,13}-test-opentelemetry-test-utils
    pypy3-test-opentelemetry-test-utils
    lint-opentelemetry-test-utils

    spellcheck
    tracecontext
    mypy,mypyinstalled
    typecheck
    docs
    docker-tests-{otlpexporter,opencensus}
    public-symbols-check
    shellcheck
    generate-workflows
    precommit

[testenv]
deps =
  lint: -r dev-requirements.txt
  coverage: pytest
  coverage: pytest-cov

  mypy,mypyinstalled: -c {toxinidir}/dev-requirements.txt
  mypy,mypyinstalled: mypy

  api: -r {toxinidir}/opentelemetry-api/test-requirements.txt

  sdk: -r {toxinidir}/opentelemetry-sdk/test-requirements.txt
  benchmark-opentelemetry-sdk: -r {toxinidir}/opentelemetry-sdk/benchmark-requirements.txt

  semantic-conventions: -r {toxinidir}/opentelemetry-semantic-conventions/test-requirements.txt

  test-utils: -r {toxinidir}/tests/opentelemetry-test-utils/test-requirements.txt

  opentelemetry-proto-protobuf5: -r {toxinidir}/opentelemetry-proto/test-requirements.txt

  exporter-opencensus: -r {toxinidir}/exporter/opentelemetry-exporter-opencensus/test-requirements.txt

  exporter-otlp-proto-common: -r {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-common/test-requirements.txt

  exporter-otlp-combined: -r {toxinidir}/exporter/opentelemetry-exporter-otlp/test-requirements.txt

  opentelemetry-exporter-otlp-proto-grpc: -r {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-grpc/test-requirements.txt
  benchmark-exporter-otlp-proto-grpc: -r {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-grpc/benchmark-requirements.txt

  opentelemetry-exporter-otlp-proto-http: -r {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-http/test-requirements.txt

  opentracing-shim: -r {toxinidir}/shim/opentelemetry-opentracing-shim/test-requirements.txt

  opencensus-shim: -r {toxinidir}/shim/opentelemetry-opencensus-shim/test-requirements.txt

  exporter-prometheus: -r {toxinidir}/exporter/opentelemetry-exporter-prometheus/test-requirements.txt

  exporter-zipkin-combined: -r {toxinidir}/exporter/opentelemetry-exporter-zipkin/test-requirements.txt

  exporter-zipkin-proto-http: -r {toxinidir}/exporter/opentelemetry-exporter-zipkin-proto-http/test-requirements.txt

  exporter-zipkin-json: -r {toxinidir}/exporter/opentelemetry-exporter-zipkin-json/test-requirements.txt

  propagator-b3: -r {toxinidir}/propagator/opentelemetry-propagator-b3/test-requirements.txt
  benchmark-opentelemetry-propagator-b3: -r {toxinidir}/propagator/opentelemetry-propagator-b3/benchmark-requirements.txt

  propagator-jaeger: -r {toxinidir}/propagator/opentelemetry-propagator-jaeger/test-requirements.txt

  mypyinstalled: -e {toxinidir}/opentelemetry-api

  getting-started: -r {toxinidir}/docs/getting_started/tests/requirements.txt
  getting-started: {env:CONTRIB_REPO}\#egg=opentelemetry-util-http&subdirectory=util/opentelemetry-util-http
  getting-started: {env:CONTRIB_REPO}\#egg=opentelemetry-instrumentation&subdirectory=opentelemetry-instrumentation
  getting-started: {env:CONTRIB_REPO}\#egg=opentelemetry-instrumentation-requests&subdirectory=instrumentation/opentelemetry-instrumentation-requests
  getting-started: {env:CONTRIB_REPO}\#egg=opentelemetry-instrumentation-wsgi&subdirectory=instrumentation/opentelemetry-instrumentation-wsgi
  getting-started: {env:CONTRIB_REPO}\#egg=opentelemetry-instrumentation-flask&subdirectory=instrumentation/opentelemetry-instrumentation-flask

allowlist_externals = sh

setenv =
  ; override CONTRIB_REPO_SHA via env variable when testing other branches/commits than main
  ; i.e: CONTRIB_REPO_SHA=dde62cebffe519c35875af6d06fae053b3be65ec tox -e <env to test>
  CONTRIB_REPO_SHA={env:CONTRIB_REPO_SHA:main}
  CONTRIB_REPO=git+https://github.com/open-telemetry/opentelemetry-python-contrib.git@{env:CONTRIB_REPO_SHA}
  mypy: MYPYPATH={toxinidir}/opentelemetry-api/src/:{toxinidir}/opentelemetry-semantic-conventions/src/:{toxinidir}/opentelemetry-sdk/src/:{toxinidir}/tests/opentelemetry-test-utils/src/
commands_pre =
  ; In order to get a healthy coverage report,
  ; we have to install packages in editable mode.
  coverage: python {toxinidir}/scripts/eachdist.py install --editable

commands =
  test-opentelemetry-api: pytest {toxinidir}/opentelemetry-api/tests {posargs}
  lint-opentelemetry-api: pylint {toxinidir}/opentelemetry-api

  test-opentelemetry-sdk: pytest {toxinidir}/opentelemetry-sdk/tests {posargs}
  lint-opentelemetry-sdk: pylint {toxinidir}/opentelemetry-sdk
  benchmark-opentelemetry-sdk: pytest {toxinidir}/opentelemetry-sdk/benchmarks --benchmark-json={toxinidir}/opentelemetry-sdk/sdk-benchmark.json {posargs}

  test-opentelemetry-proto-protobuf5: pytest {toxinidir}/opentelemetry-proto/tests {posargs}
  lint-opentelemetry-proto-protobuf5: pylint {toxinidir}/opentelemetry-proto

  test-opentelemetry-semantic-conventions: pytest {toxinidir}/opentelemetry-semantic-conventions/tests {posargs}
  lint-opentelemetry-semantic-conventions: pylint {toxinidir}/opentelemetry-semantic-conventions

  test-opentelemetry-getting-started: pytest {toxinidir}/docs/getting_started/tests {posargs}
  lint-opentelemetry-getting-started: pylint {toxinidir}/docs/getting_started

  test-opentelemetry-opentracing-shim: pytest {toxinidir}/shim/opentelemetry-opentracing-shim/tests {posargs}
  lint-opentelemetry-opentracing-shim: sh -c "cd shim && pylint --rcfile ../.pylintrc {toxinidir}/shim/opentelemetry-opentracing-shim"

  test-opentelemetry-opencensus-shim: pytest {toxinidir}/shim/opentelemetry-opencensus-shim/tests {posargs}
  lint-opentelemetry-opencensus-shim: sh -c "cd shim && pylint --rcfile ../.pylintrc {toxinidir}/shim/opentelemetry-opencensus-shim"

  test-opentelemetry-exporter-opencensus: pytest {toxinidir}/exporter/opentelemetry-exporter-opencensus/tests {posargs}
  lint-opentelemetry-exporter-opencensus: sh -c "cd exporter && pylint --rcfile ../.pylintrc {toxinidir}/exporter/opentelemetry-exporter-opencensus"

  test-opentelemetry-exporter-otlp-proto-common: pytest {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-common/tests {posargs}
  lint-opentelemetry-exporter-otlp-proto-common: sh -c "cd exporter && pylint --prefer-stubs yes --rcfile ../.pylintrc {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-common"

  test-opentelemetry-exporter-otlp-combined: pytest {toxinidir}/exporter/opentelemetry-exporter-otlp/tests {posargs}
  lint-opentelemetry-exporter-otlp-combined: sh -c "cd exporter && pylint --rcfile ../.pylintrc {toxinidir}/exporter/opentelemetry-exporter-otlp"

  test-opentelemetry-exporter-otlp-proto-grpc: pytest {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-grpc/tests {posargs}
  lint-opentelemetry-exporter-otlp-proto-grpc: sh -c "cd exporter && pylint --prefer-stubs yes --rcfile ../.pylintrc {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-grpc"
  benchmark-opentelemetry-exporter-otlp-proto-grpc: pytest {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-grpc/benchmarks --benchmark-json=exporter-otlp-proto-grpc-benchmark.json {posargs}

  test-opentelemetry-exporter-otlp-proto-http: pytest {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-http/tests {posargs}
  lint-opentelemetry-exporter-otlp-proto-http: sh -c "cd exporter && pylint --prefer-stubs yes --rcfile ../.pylintrc {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-http"

  test-opentelemetry-exporter-prometheus: pytest {toxinidir}/exporter/opentelemetry-exporter-prometheus/tests {posargs}
  lint-opentelemetry-exporter-prometheus: sh -c "cd exporter && pylint --rcfile ../.pylintrc {toxinidir}/exporter/opentelemetry-exporter-prometheus"

  test-opentelemetry-exporter-zipkin-combined: pytest {toxinidir}/exporter/opentelemetry-exporter-zipkin/tests {posargs}
  lint-opentelemetry-exporter-zipkin-combined: sh -c "cd exporter && pylint --rcfile ../.pylintrc {toxinidir}/exporter/opentelemetry-exporter-zipkin"

  test-opentelemetry-exporter-zipkin-proto-http: pytest {toxinidir}/exporter/opentelemetry-exporter-zipkin-proto-http/tests {posargs}
  lint-opentelemetry-exporter-zipkin-proto-http: sh -c "cd exporter && pylint --rcfile ../.pylintrc {toxinidir}/exporter/opentelemetry-exporter-zipkin-proto-http"

  test-opentelemetry-exporter-zipkin-json: pytest {toxinidir}/exporter/opentelemetry-exporter-zipkin-json/tests {posargs}
  lint-opentelemetry-exporter-zipkin-json: sh -c "cd exporter && pylint --rcfile ../.pylintrc {toxinidir}/exporter/opentelemetry-exporter-zipkin-json"

  test-opentelemetry-propagator-b3: pytest {toxinidir}/propagator/opentelemetry-propagator-b3/tests {posargs}
  lint-opentelemetry-propagator-b3: sh -c "cd propagator && pylint --rcfile ../.pylintrc {toxinidir}/propagator/opentelemetry-propagator-b3"
  benchmark-opentelemetry-propagator-b3: pytest {toxinidir}/propagator/opentelemetry-propagator-b3/benchmarks --benchmark-json=propagator-b3-benchmark.json {posargs}

  test-opentelemetry-propagator-jaeger: pytest {toxinidir}/propagator/opentelemetry-propagator-jaeger/tests {posargs}
  lint-opentelemetry-propagator-jaeger: sh -c "cd propagator && pylint --rcfile ../.pylintrc {toxinidir}/propagator/opentelemetry-propagator-jaeger"

  test-opentelemetry-test-utils: pytest {toxinidir}/tests/opentelemetry-test-utils/tests {posargs}
  lint-opentelemetry-test-utils: sh -c "cd tests && pylint --rcfile ../.pylintrc {toxinidir}/tests/opentelemetry-test-utils"

  coverage: {toxinidir}/scripts/coverage.sh

  mypy: mypy --version
  mypy: mypy --install-types --non-interactive --namespace-packages --explicit-package-bases opentelemetry-api/src/opentelemetry/
  mypy: mypy --install-types --non-interactive --namespace-packages --explicit-package-bases opentelemetry-sdk/src/opentelemetry/sdk/resources
  mypy: mypy --install-types --non-interactive --namespace-packages --explicit-package-bases opentelemetry-semantic-conventions/src/opentelemetry/semconv/

  ; For test code, we don't want to enforce the full mypy strictness
  mypy: mypy --install-types --non-interactive --namespace-packages --config-file=mypy-relaxed.ini opentelemetry-api/tests/

  ; Test that mypy can pick up typeinfo from an installed package (otherwise,
  ; implicit Any due to unfollowed import would result).
  mypyinstalled: mypy --install-types --non-interactive --namespace-packages opentelemetry-api/tests/mypysmoke.py --strict

[testenv:spellcheck]
basepython: python3
recreate = True
deps =
  codespell==2.2.6

commands =
  codespell

[testenv:docs]
basepython: python3
recreate = True
deps =
  -c {toxinidir}/dev-requirements.txt
  -r {toxinidir}/docs-requirements.txt
setenv =
  ; We need this workaround to allow generating docs for exporters that have different protobuf versions as requirement.
  ; See https://github.com/open-telemetry/opentelemetry-python/pull/4206
  ; We can remove the workaround when opentelemetry-exporter-zipkin-proto-http support protobuf > 5.26
  PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python
changedir = docs
commands =
  sphinx-build -E -a -W -b html -T . _build/html

[testenv:tracecontext]
basepython: python3
deps =
  # needed for tracecontext
  aiohttp~=3.6
  pytest==7.4.4
  # needed for example trace integration
  flask~=2.3
  requests~=2.7
  markupsafe~=2.1
  -e {toxinidir}/opentelemetry-api
  -e {toxinidir}/opentelemetry-semantic-conventions
  -e {toxinidir}/opentelemetry-sdk
  -e {env:CONTRIB_REPO}\#egg=opentelemetry-util-http&subdirectory=util/opentelemetry-util-http
  -e {env:CONTRIB_REPO}\#egg=opentelemetry-instrumentation&subdirectory=opentelemetry-instrumentation
  -e {env:CONTRIB_REPO}\#egg=opentelemetry-instrumentation-requests&subdirectory=instrumentation/opentelemetry-instrumentation-requests
  -e {env:CONTRIB_REPO}\#egg=opentelemetry-instrumentation-wsgi&subdirectory=instrumentation/opentelemetry-instrumentation-wsgi

allowlist_externals =
  {toxinidir}/scripts/tracecontext-integration-test.sh

commands =
  {toxinidir}/scripts/tracecontext-integration-test.sh

[testenv:docker-tests-{otlpexporter,opencensus}]
deps =
  pytest==7.1.3
  # Pinning PyYAML for issue: https://github.com/yaml/pyyaml/issues/724
  PyYAML==5.3.1
  # Pinning docker for issue: https://github.com/docker/compose/issues/11309
  docker<7
  docker-compose==1.29.2
  requests==2.28.2
  ; core packages
  -e {toxinidir}/opentelemetry-api
  -e {toxinidir}/opentelemetry-semantic-conventions
  -e {toxinidir}/opentelemetry-sdk
  -e {toxinidir}/tests/opentelemetry-test-utils
  ; OTLP packages
  otlpexporter: -e {toxinidir}/opentelemetry-proto
  otlpexporter: -e {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-common
  otlpexporter: -e {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-grpc
  otlpexporter: -e {toxinidir}/exporter/opentelemetry-exporter-otlp-proto-http
  otlpexporter: -e {toxinidir}/exporter/opentelemetry-exporter-otlp

  opencensus: -e {toxinidir}/exporter/opentelemetry-exporter-opencensus

changedir =
  tests/opentelemetry-docker-tests/tests

commands_pre =
  pip freeze
  docker-compose up -d
commands =
  otlpexporter: pytest otlpexporter {posargs}
  opencensus: pytest opencensus {posargs}

commands_post =
  docker-compose down -v

[testenv:public-symbols-check]
basepython: python3
recreate = True
deps =
  GitPython==3.1.40
commands =
  python {toxinidir}/scripts/public_symbols_checker.py

[testenv:generate-workflows]
deps =
  tox
  Jinja2
commands =
  python {toxinidir}/.github/workflows/generate_workflows.py

[testenv:shellcheck]
commands_pre =
  sh -c "sudo apt update -y && sudo apt install --assume-yes shellcheck"
commands =
  sh -c "find {toxinidir} -name \*.sh | xargs shellcheck --severity=warning"

[testenv:typecheck]
basepython: python3
deps =
  -c {toxinidir}/dev-requirements.txt
  pyright
  psutil
  -e {toxinidir}/opentelemetry-api
  -e {toxinidir}/opentelemetry-semantic-conventions
  -e {toxinidir}/opentelemetry-sdk
  -e {toxinidir}/tests/opentelemetry-test-utils
commands =
  pyright --version
  pyright

[testenv:{precommit,ruff}]
basepython: python3
deps =
  -c {toxinidir}/dev-requirements.txt
  pre-commit
commands =
  pre-commit run --color=always --all-files {posargs}
