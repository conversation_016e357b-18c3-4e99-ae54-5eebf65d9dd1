[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-sdk"
dynamic = ["version"]
description = "OpenTelemetry Python SDK"
readme = "README.rst"
license = {text = "Apache-2.0"}
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 5 - Production/Stable",
  "Framework :: OpenTelemetry",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Typing :: Typed",
]
dependencies = [
  "opentelemetry-api == 1.33.0.dev",
  "opentelemetry-semantic-conventions == 0.54b0.dev",
  "typing-extensions >= 3.7.4",
]

[project.entry-points.opentelemetry_environment_variables]
sdk = "opentelemetry.sdk.environment_variables"

[project.entry-points.opentelemetry_id_generator]
random = "opentelemetry.sdk.trace.id_generator:RandomIdGenerator"

[project.entry-points.opentelemetry_traces_sampler]
always_on = "opentelemetry.sdk.trace.sampling:_AlwaysOn"
always_off = "opentelemetry.sdk.trace.sampling:_AlwaysOff"
parentbased_always_on = "opentelemetry.sdk.trace.sampling:_ParentBasedAlwaysOn"
parentbased_always_off = "opentelemetry.sdk.trace.sampling:_ParentBasedAlwaysOff"
traceidratio = "opentelemetry.sdk.trace.sampling:TraceIdRatioBased"
parentbased_traceidratio = "opentelemetry.sdk.trace.sampling:ParentBasedTraceIdRatio"

[project.entry-points.opentelemetry_logger_provider]
sdk_logger_provider = "opentelemetry.sdk._logs:LoggerProvider"

[project.entry-points.opentelemetry_logs_exporter]
console = "opentelemetry.sdk._logs.export:ConsoleLogExporter"

[project.entry-points.opentelemetry_meter_provider]
sdk_meter_provider = "opentelemetry.sdk.metrics:MeterProvider"

[project.entry-points.opentelemetry_metrics_exporter]
console = "opentelemetry.sdk.metrics.export:ConsoleMetricExporter"

[project.entry-points.opentelemetry_tracer_provider]
sdk_tracer_provider = "opentelemetry.sdk.trace:TracerProvider"

[project.entry-points.opentelemetry_traces_exporter]
console = "opentelemetry.sdk.trace.export:ConsoleSpanExporter"

[project.entry-points.opentelemetry_resource_detector]
otel = "opentelemetry.sdk.resources:OTELResourceDetector"
process = "opentelemetry.sdk.resources:ProcessResourceDetector"
os = "opentelemetry.sdk.resources:OsResourceDetector"
host = "opentelemetry.sdk.resources:_HostResourceDetector"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python/tree/main/opentelemetry-sdk"
Repository = "https://github.com/open-telemetry/opentelemetry-python"

[tool.hatch.version]
path = "src/opentelemetry/sdk/version/__init__.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
