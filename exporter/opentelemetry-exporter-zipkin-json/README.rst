OpenTelemetry Zipkin JSON Exporter
==================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-exporter-zipkin-json.svg
   :target: https://pypi.org/project/opentelemetry-exporter-zipkin-json/

This library allows export of tracing data to `Zipkin <https://zipkin.io/>`_ using JSON
for serialization.

Installation
------------

::

     pip install opentelemetry-exporter-zipkin-json


References
----------

* `OpenTelemetry Zipkin Exporter <https://opentelemetry-python.readthedocs.io/en/latest/exporter/zipkin/zipkin.html>`_
* `Zipkin <https://zipkin.io/>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
