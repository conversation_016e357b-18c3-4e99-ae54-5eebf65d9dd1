OpenTelemetry Protobuf Encoding
===============================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-exporter-otlp-proto-common.svg
   :target: https://pypi.org/project/opentelemetry-exporter-otlp-proto-common/

This library is provided as a convenience to encode to Protobuf. Currently used by:

* opentelemetry-exporter-otlp-proto-grpc
* opentelemetry-exporter-otlp-proto-http


Installation
------------

::

     pip install opentelemetry-exporter-otlp-proto-common


References
----------

* `OpenTelemetry <https://opentelemetry.io/>`_
* `OpenTelemetry Protocol Specification <https://github.com/open-telemetry/oteps/blob/main/text/0035-opentelemetry-protocol.md>`_
