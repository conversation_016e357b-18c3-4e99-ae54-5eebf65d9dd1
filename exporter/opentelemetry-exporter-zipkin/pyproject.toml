[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-exporter-zipkin"
dynamic = ["version"]
description = "Zipkin Span Exporters for OpenTelemetry"
readme = "README.rst"
license = {text = "Apache-2.0"}
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 5 - Production/Stable",
  "Framework :: OpenTelemetry",
  "Framework :: OpenTelemetry :: Exporters",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Typing :: Typed",
]
dependencies = [
  "opentelemetry-exporter-zipkin-json == 1.33.0.dev",
  "opentelemetry-exporter-zipkin-proto-http == 1.33.0.dev",
]

[project.entry-points.opentelemetry_traces_exporter]
zipkin = "opentelemetry.exporter.zipkin.proto.http:ZipkinExporter"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python/tree/main/exporter/opentelemetry-exporter-zipkin"
Repository = "https://github.com/open-telemetry/opentelemetry-python"

[tool.hatch.version]
path = "src/opentelemetry/exporter/zipkin/version/__init__.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
