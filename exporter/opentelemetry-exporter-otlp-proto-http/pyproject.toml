[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-exporter-otlp-proto-http"
dynamic = ["version"]
description = "OpenTelemetry Collector Protobuf over HTTP Exporter"
readme = "README.rst"
license = {text = "Apache-2.0"}
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 5 - Production/Stable",
  "Framework :: OpenTelemetry",
  "Framework :: OpenTelemetry :: Exporters",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
]
dependencies = [
  "Deprecated >= 1.2.6",
  "googleapis-common-protos ~= 1.52",
  "opentelemetry-api ~= 1.15",
  "opentelemetry-proto == 1.33.0.dev",
  "opentelemetry-sdk ~= 1.33.0.dev",
  "opentelemetry-exporter-otlp-proto-common == 1.33.0.dev",
  "requests ~= 2.7",
]

[project.entry-points.opentelemetry_traces_exporter]
otlp_proto_http = "opentelemetry.exporter.otlp.proto.http.trace_exporter:OTLPSpanExporter"

[project.entry-points.opentelemetry_metrics_exporter]
otlp_proto_http = "opentelemetry.exporter.otlp.proto.http.metric_exporter:OTLPMetricExporter"

[project.entry-points.opentelemetry_logs_exporter]
otlp_proto_http = "opentelemetry.exporter.otlp.proto.http._log_exporter:OTLPLogExporter"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python/tree/main/exporter/opentelemetry-exporter-otlp-proto-http"
Repository = "https://github.com/open-telemetry/opentelemetry-python"

[tool.hatch.version]
path = "src/opentelemetry/exporter/otlp/proto/http/version/__init__.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
