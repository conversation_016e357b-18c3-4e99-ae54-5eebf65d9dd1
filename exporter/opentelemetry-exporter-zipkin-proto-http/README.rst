OpenTelemetry Zipkin Protobuf Exporter
======================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-exporter-zipkin-proto-http.svg
   :target: https://pypi.org/project/opentelemetry-exporter-zipkin-proto-http/

This library allows export of tracing data to `Zipkin <https://zipkin.io/>`_ using Protobuf
for serialization.

Installation
------------

::

     pip install opentelemetry-exporter-zipkin-proto-http


References
----------

* `OpenTelemetry Zipkin Exporter <https://opentelemetry-python.readthedocs.io/en/latest/exporter/zipkin/zipkin.html>`_
* `Zipkin <https://zipkin.io/>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
