[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-exporter-prometheus"
dynamic = ["version"]
description = "Prometheus Metric Exporter for OpenTelemetry"
readme = "README.rst"
license = {text = "Apache-2.0"}
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Framework :: OpenTelemetry",
  "Framework :: OpenTelemetry :: Exporters",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
]
dependencies = [
  "opentelemetry-api ~= 1.12",
  # DONOTMERGE: confirm that this will becomes ~= 1.21 in the next release
  "opentelemetry-sdk ~= 1.33.0.dev",
  "prometheus_client >= 0.5.0, < 1.0.0",
]

[project.entry-points.opentelemetry_metrics_exporter]
prometheus = "opentelemetry.exporter.prometheus:_AutoPrometheusMetricReader"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python/tree/main/exporter/opentelemetry-exporter-prometheus"
Repository = "https://github.com/open-telemetry/opentelemetry-python"

[tool.hatch.version]
path = "src/opentelemetry/exporter/prometheus/version/__init__.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
