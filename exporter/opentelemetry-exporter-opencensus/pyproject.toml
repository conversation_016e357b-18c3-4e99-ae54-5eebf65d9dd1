[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-exporter-opencensus"
dynamic = ["version"]
description = "OpenCensus Exporter"
readme = "README.rst"
license = {text = "Apache-2.0"}
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Framework :: OpenTelemetry",
  "Framework :: OpenTelemetry :: Exporters",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Typing :: Typed",
]
dependencies = [
  "grpcio >= 1.63.2, < 2.0.0; python_version < '3.13'",
  "grpcio >= 1.66.2, < 2.0.0; python_version >= '3.13'",
  "opencensus-proto >= 0.1.0, < 1.0.0",
  "opentelemetry-api >= 1.33.0.dev",
  "opentelemetry-sdk >= 1.15",
  "protobuf ~= 3.13",
  "setuptools >= 16.0",
]

[project.entry-points.opentelemetry_traces_exporter]
opencensus = "opentelemetry.exporter.opencensus.trace_exporter:OpenCensusSpanExporter"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python/tree/main/exporter/opentelemetry-exporter-opencensus"
Repository = "https://github.com/open-telemetry/opentelemetry-python"

[tool.hatch.version]
path = "src/opentelemetry/exporter/opencensus/version/__init__.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
