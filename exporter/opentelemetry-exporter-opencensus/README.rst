OpenCensus Exporter
===================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-exporter-opencensus.svg
   :target: https://pypi.org/project/opentelemetry-exporter-opencensus/

This library allows to export traces using OpenCensus.

Installation
------------

::

     pip install opentelemetry-exporter-opencensus


References
----------

* `OpenCensus Exporter <https://opentelemetry-python.readthedocs.io/en/latest/exporter/opencensus/opencensus.html>`_
* `OpenTelemetry Collector <https://github.com/open-telemetry/opentelemetry-collector/>`_
* `OpenTelemetry <https://opentelemetry.io/>`_
