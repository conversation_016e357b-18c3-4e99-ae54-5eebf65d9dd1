; This is mainly intended for unit tests and such. So probably going forward, we
; will disable even more warnings here.
[mypy]
  disallow_any_unimported = True
; disallow_any_expr = True
  disallow_any_decorated = True
; disallow_any_explicit = True
  disallow_any_generics = True
  disallow_subclassing_any = True
  disallow_untyped_calls = True
; disallow_untyped_defs = True
  disallow_incomplete_defs = True
  check_untyped_defs = True
  disallow_untyped_decorators = True
  allow_untyped_globals = True
; Due to disabling some other warnings, unused ignores may occur.
;  warn_unused_ignores = True
  warn_return_any = True
  strict_equality = True

[mypy-setuptools]
  ignore_missing_imports = True
