[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-propagator-jaeger"
dynamic = ["version"]
description = "OpenTelemetry Jaeger Propagator"
readme = "README.rst"
license = {text = "Apache-2.0"}
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 5 - Production/Stable",
  "Framework :: OpenTelemetry",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Typing :: Typed",
]
dependencies = [
  "opentelemetry-api ~= 1.3",
]

[project.entry-points.opentelemetry_propagator]
jaeger = "opentelemetry.propagators.jaeger:JaegerPropagator"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python/tree/main/propagator/opentelemetry-propagator-jaeger"
Repository = "https://github.com/open-telemetry/opentelemetry-python"

[tool.hatch.version]
path = "src/opentelemetry/propagators/jaeger/version/__init__.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
