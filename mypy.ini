[mypy]
  disallow_any_unimported = True
  disallow_any_expr = True
  disallow_any_decorated = True
; disallow_any_explicit = True
  disallow_any_generics = True
  disallow_subclassing_any = True
  disallow_untyped_calls = True
  disallow_untyped_defs = True
  disallow_incomplete_defs = True
  check_untyped_defs = True
  disallow_untyped_decorators = True
  warn_unused_configs = True
  warn_unused_ignores = True
  warn_return_any = True
  warn_redundant_casts = True
  strict_equality = True
  strict_optional = True
  no_implicit_optional =  True
  no_implicit_reexport = True
  # https://mypy.readthedocs.io/en/stable/running_mypy.html#follow-imports
  follow_imports = silent
