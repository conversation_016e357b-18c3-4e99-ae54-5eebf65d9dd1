[project]
name = "opentelemetry-python"
version = "0.0.0" # This is not used.
requires-python = ">=3.8"
dependencies = [
  "opentelemetry-api",
  "opentelemetry-sdk",
  "opentelemetry-semantic-conventions",
  "opentelemetry-proto",
  "opentelemetry-test-utils",
  "opentelemetry-exporter-otlp-proto-grpc",
  "opentelemetry-exporter-otlp-proto-http",
  "opentelemetry-exporter-otlp-proto-common",
  "opentelemetry-exporter-zipkin-json",
  "opentelemetry-exporter-prometheus",
  "opentelemetry-propagator-jaeger",
  "opentelemetry-propagator-b3",
]

# https://docs.astral.sh/uv/reference/settings/
[tool.uv]
package = false # https://docs.astral.sh/uv/reference/settings/#package
required-version = ">=0.6.0"

[tool.uv.sources]
opentelemetry-api = { workspace = true}
opentelemetry-sdk = { workspace = true }
opentelemetry-proto = { workspace = true }
opentelemetry-semantic-conventions = { workspace = true }
opentelemetry-test-utils = { workspace = true }
opentelemetry-exporter-otlp-proto-grpc = { workspace = true }
opentelemetry-exporter-otlp-proto-http = { workspace = true }
opentelemetry-exporter-otlp-proto-common = { workspace = true }
opentelemetry-exporter-zipkin-json = { workspace = true }
opentelemetry-exporter-prometheus = {workspace = true }
opentelemetry-propagator-jaeger = { workspace = true }
opentelemetry-propagator-b3 = { workspace = true }

[tool.uv.workspace]
members = [
  "opentelemetry-api",
  "opentelemetry-sdk",
  "opentelemetry-semantic-conventions",
  "opentelemetry-proto",
  "exporter/*",
  "propagator/*",
  "tests/opentelemetry-test-utils",
]

exclude = [
  "exporter/opentelemetry-exporter-opencensus",
  "exporter/opentelemetry-exporter-zipkin",
  "exporter/opentelemetry-exporter-zipkin-proto-http",
]

[tool.pytest.ini_options]
addopts = "-rs -v"
log_cli = true

[tool.ruff]
# https://docs.astral.sh/ruff/configuration/
target-version = "py38"
line-length = 79
extend-exclude = [
  "*_pb2*.py*",
]
output-format = "concise"

[tool.ruff.lint]
# https://docs.astral.sh/ruff/linter/#rule-selection
# pylint: https://github.com/astral-sh/ruff/issues/970
select = [
  "I",   # isort
  "F",   # pyflakes
  "E",   # pycodestyle errors
  "W",   # pycodestyle warnings
  "PLC", # pylint convention
  "PLE", # pylint error
  "Q",   # flake8-quotes
  "G",   # https://docs.astral.sh/ruff/rules/#flake8-logging-format-g
]

ignore = [
  "E501", # line-too-long
]

[tool.ruff.lint.per-file-ignores]
"docs/**/*.*" = ["PLE"]

[tool.ruff.lint.isort]
known-third-party = [
  "psutil",
  "pytest",
  "redis",
  "redis_opentracing",
  "opencensus",
]
known-first-party = ["opentelemetry", "opentelemetry_example_app"]

[tool.pyright]
typeCheckingMode = "standard"
pythonVersion = "3.8"

include = [
  "opentelemetry-semantic-conventions",
  "opentelemetry-api",
  "opentelemetry-sdk",
]

exclude = [
  "opentelemetry-sdk/tests",
  "opentelemetry-sdk/src/opentelemetry/sdk/_configuration",
  "opentelemetry-sdk/src/opentelemetry/sdk/_events",
  "opentelemetry-sdk/src/opentelemetry/sdk/_logs",
  "opentelemetry-sdk/src/opentelemetry/sdk/error_handler",
  "opentelemetry-sdk/src/opentelemetry/sdk/resources",
  "opentelemetry-sdk/src/opentelemetry/sdk/metrics",
  "opentelemetry-sdk/src/opentelemetry/sdk/trace",
  "opentelemetry-sdk/src/opentelemetry/sdk/util",
  "opentelemetry-sdk/benchmarks",
]

# When packages are correct typed add them to the strict list
strict = [
  "opentelemetry-semantic-conventions",
  "opentelemetry-sdk/src/opentelemetry/sdk/environment_variables",
]
