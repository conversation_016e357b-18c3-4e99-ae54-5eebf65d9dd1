"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2019, OpenTelemetry Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import opentelemetry.proto.common.v1.common_pb2
import opentelemetry.proto.resource.v1.resource_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _SpanFlags:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _SpanFlagsEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _SpanFlags.ValueType
    ],
    builtins.type,
):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    SPAN_FLAGS_DO_NOT_USE: _SpanFlags.ValueType  # 0
    """The zero value for the enum. Should not be used for comparisons.
    Instead use bitwise "and" with the appropriate mask as shown above.
    """
    SPAN_FLAGS_TRACE_FLAGS_MASK: _SpanFlags.ValueType  # 255
    """Bits 0-7 are used for trace flags."""
    SPAN_FLAGS_CONTEXT_HAS_IS_REMOTE_MASK: _SpanFlags.ValueType  # 256
    """Bits 8 and 9 are used to indicate that the parent span or link span is remote.
    Bit 8 (`HAS_IS_REMOTE`) indicates whether the value is known.
    Bit 9 (`IS_REMOTE`) indicates whether the span or link is remote.
    """
    SPAN_FLAGS_CONTEXT_IS_REMOTE_MASK: _SpanFlags.ValueType  # 512

class SpanFlags(_SpanFlags, metaclass=_SpanFlagsEnumTypeWrapper):
    """SpanFlags represents constants used to interpret the
    Span.flags field, which is protobuf 'fixed32' type and is to
    be used as bit-fields. Each non-zero value defined in this enum is
    a bit-mask.  To extract the bit-field, for example, use an
    expression like:

      (span.flags & SPAN_FLAGS_TRACE_FLAGS_MASK)

    See https://www.w3.org/TR/trace-context-2/#trace-flags for the flag definitions.

    Note that Span flags were introduced in version 1.1 of the
    OpenTelemetry protocol.  Older Span producers do not set this
    field, consequently consumers should not rely on the absence of a
    particular flag bit to indicate the presence of a particular feature.
    """

SPAN_FLAGS_DO_NOT_USE: SpanFlags.ValueType  # 0
"""The zero value for the enum. Should not be used for comparisons.
Instead use bitwise "and" with the appropriate mask as shown above.
"""
SPAN_FLAGS_TRACE_FLAGS_MASK: SpanFlags.ValueType  # 255
"""Bits 0-7 are used for trace flags."""
SPAN_FLAGS_CONTEXT_HAS_IS_REMOTE_MASK: SpanFlags.ValueType  # 256
"""Bits 8 and 9 are used to indicate that the parent span or link span is remote.
Bit 8 (`HAS_IS_REMOTE`) indicates whether the value is known.
Bit 9 (`IS_REMOTE`) indicates whether the span or link is remote.
"""
SPAN_FLAGS_CONTEXT_IS_REMOTE_MASK: SpanFlags.ValueType  # 512
global___SpanFlags = SpanFlags

@typing_extensions.final
class TracesData(google.protobuf.message.Message):
    """TracesData represents the traces data that can be stored in a persistent storage,
    OR can be embedded by other protocols that transfer OTLP traces data but do
    not implement the OTLP protocol.

    The main difference between this message and collector protocol is that
    in this message there will not be any "control" or "metadata" specific to
    OTLP protocol.

    When new fields are added into this message, the OTLP request MUST be updated
    as well.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESOURCE_SPANS_FIELD_NUMBER: builtins.int
    @property
    def resource_spans(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___ResourceSpans
    ]:
        """An array of ResourceSpans.
        For data coming from a single resource this array will typically contain
        one element. Intermediary nodes that receive data from multiple origins
        typically batch the data before forwarding further and in that case this
        array will contain multiple elements.
        """

    def __init__(
        self,
        *,
        resource_spans: (
            collections.abc.Iterable[global___ResourceSpans] | None
        ) = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "resource_spans", b"resource_spans"
        ],
    ) -> None: ...

global___TracesData = TracesData

@typing_extensions.final
class ResourceSpans(google.protobuf.message.Message):
    """A collection of ScopeSpans from a Resource."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESOURCE_FIELD_NUMBER: builtins.int
    SCOPE_SPANS_FIELD_NUMBER: builtins.int
    SCHEMA_URL_FIELD_NUMBER: builtins.int
    @property
    def resource(
        self,
    ) -> opentelemetry.proto.resource.v1.resource_pb2.Resource:
        """The resource for the spans in this message.
        If this field is not set then no resource info is known.
        """

    @property
    def scope_spans(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___ScopeSpans
    ]:
        """A list of ScopeSpans that originate from a resource."""
    schema_url: builtins.str
    """The Schema URL, if known. This is the identifier of the Schema that the resource data
    is recorded in. To learn more about Schema URL see
    https://opentelemetry.io/docs/specs/otel/schemas/#schema-url
    This schema_url applies to the data in the "resource" field. It does not apply
    to the data in the "scope_spans" field which have their own schema_url field.
    """
    def __init__(
        self,
        *,
        resource: (
            opentelemetry.proto.resource.v1.resource_pb2.Resource | None
        ) = ...,
        scope_spans: (
            collections.abc.Iterable[global___ScopeSpans] | None
        ) = ...,
        schema_url: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["resource", b"resource"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "resource",
            b"resource",
            "schema_url",
            b"schema_url",
            "scope_spans",
            b"scope_spans",
        ],
    ) -> None: ...

global___ResourceSpans = ResourceSpans

@typing_extensions.final
class ScopeSpans(google.protobuf.message.Message):
    """A collection of Spans produced by an InstrumentationScope."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SCOPE_FIELD_NUMBER: builtins.int
    SPANS_FIELD_NUMBER: builtins.int
    SCHEMA_URL_FIELD_NUMBER: builtins.int
    @property
    def scope(
        self,
    ) -> opentelemetry.proto.common.v1.common_pb2.InstrumentationScope:
        """The instrumentation scope information for the spans in this message.
        Semantically when InstrumentationScope isn't set, it is equivalent with
        an empty instrumentation scope name (unknown).
        """

    @property
    def spans(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___Span
    ]:
        """A list of Spans that originate from an instrumentation scope."""
    schema_url: builtins.str
    """The Schema URL, if known. This is the identifier of the Schema that the span data
    is recorded in. To learn more about Schema URL see
    https://opentelemetry.io/docs/specs/otel/schemas/#schema-url
    This schema_url applies to all spans and span events in the "spans" field.
    """
    def __init__(
        self,
        *,
        scope: (
            opentelemetry.proto.common.v1.common_pb2.InstrumentationScope
            | None
        ) = ...,
        spans: collections.abc.Iterable[global___Span] | None = ...,
        schema_url: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["scope", b"scope"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "schema_url", b"schema_url", "scope", b"scope", "spans", b"spans"
        ],
    ) -> None: ...

global___ScopeSpans = ScopeSpans

@typing_extensions.final
class Span(google.protobuf.message.Message):
    """A Span represents a single operation performed by a single component of the system.

    The next available field id is 17.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _SpanKind:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _SpanKindEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
            Span._SpanKind.ValueType
        ],
        builtins.type,
    ):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        SPAN_KIND_UNSPECIFIED: Span._SpanKind.ValueType  # 0
        """Unspecified. Do NOT use as default.
        Implementations MAY assume SpanKind to be INTERNAL when receiving UNSPECIFIED.
        """
        SPAN_KIND_INTERNAL: Span._SpanKind.ValueType  # 1
        """Indicates that the span represents an internal operation within an application,
        as opposed to an operation happening at the boundaries. Default value.
        """
        SPAN_KIND_SERVER: Span._SpanKind.ValueType  # 2
        """Indicates that the span covers server-side handling of an RPC or other
        remote network request.
        """
        SPAN_KIND_CLIENT: Span._SpanKind.ValueType  # 3
        """Indicates that the span describes a request to some remote service."""
        SPAN_KIND_PRODUCER: Span._SpanKind.ValueType  # 4
        """Indicates that the span describes a producer sending a message to a broker.
        Unlike CLIENT and SERVER, there is often no direct critical path latency relationship
        between producer and consumer spans. A PRODUCER span ends when the message was accepted
        by the broker while the logical processing of the message might span a much longer time.
        """
        SPAN_KIND_CONSUMER: Span._SpanKind.ValueType  # 5
        """Indicates that the span describes consumer receiving a message from a broker.
        Like the PRODUCER kind, there is often no direct critical path latency relationship
        between producer and consumer spans.
        """

    class SpanKind(_SpanKind, metaclass=_SpanKindEnumTypeWrapper):
        """SpanKind is the type of span. Can be used to specify additional relationships between spans
        in addition to a parent/child relationship.
        """

    SPAN_KIND_UNSPECIFIED: Span.SpanKind.ValueType  # 0
    """Unspecified. Do NOT use as default.
    Implementations MAY assume SpanKind to be INTERNAL when receiving UNSPECIFIED.
    """
    SPAN_KIND_INTERNAL: Span.SpanKind.ValueType  # 1
    """Indicates that the span represents an internal operation within an application,
    as opposed to an operation happening at the boundaries. Default value.
    """
    SPAN_KIND_SERVER: Span.SpanKind.ValueType  # 2
    """Indicates that the span covers server-side handling of an RPC or other
    remote network request.
    """
    SPAN_KIND_CLIENT: Span.SpanKind.ValueType  # 3
    """Indicates that the span describes a request to some remote service."""
    SPAN_KIND_PRODUCER: Span.SpanKind.ValueType  # 4
    """Indicates that the span describes a producer sending a message to a broker.
    Unlike CLIENT and SERVER, there is often no direct critical path latency relationship
    between producer and consumer spans. A PRODUCER span ends when the message was accepted
    by the broker while the logical processing of the message might span a much longer time.
    """
    SPAN_KIND_CONSUMER: Span.SpanKind.ValueType  # 5
    """Indicates that the span describes consumer receiving a message from a broker.
    Like the PRODUCER kind, there is often no direct critical path latency relationship
    between producer and consumer spans.
    """

    @typing_extensions.final
    class Event(google.protobuf.message.Message):
        """Event is a time-stamped annotation of the span, consisting of user-supplied
        text description and key-value pairs.
        """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
        NAME_FIELD_NUMBER: builtins.int
        ATTRIBUTES_FIELD_NUMBER: builtins.int
        DROPPED_ATTRIBUTES_COUNT_FIELD_NUMBER: builtins.int
        time_unix_nano: builtins.int
        """time_unix_nano is the time the event occurred."""
        name: builtins.str
        """name of the event.
        This field is semantically required to be set to non-empty string.
        """
        @property
        def attributes(
            self,
        ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
            opentelemetry.proto.common.v1.common_pb2.KeyValue
        ]:
            """attributes is a collection of attribute key/value pairs on the event.
            Attribute keys MUST be unique (it is not allowed to have more than one
            attribute with the same key).
            """
        dropped_attributes_count: builtins.int
        """dropped_attributes_count is the number of dropped attributes. If the value is 0,
        then no attributes were dropped.
        """
        def __init__(
            self,
            *,
            time_unix_nano: builtins.int = ...,
            name: builtins.str = ...,
            attributes: (
                collections.abc.Iterable[
                    opentelemetry.proto.common.v1.common_pb2.KeyValue
                ]
                | None
            ) = ...,
            dropped_attributes_count: builtins.int = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal[
                "attributes",
                b"attributes",
                "dropped_attributes_count",
                b"dropped_attributes_count",
                "name",
                b"name",
                "time_unix_nano",
                b"time_unix_nano",
            ],
        ) -> None: ...

    @typing_extensions.final
    class Link(google.protobuf.message.Message):
        """A pointer from the current span to another span in the same trace or in a
        different trace. For example, this can be used in batching operations,
        where a single batch handler processes multiple requests from different
        traces or when the handler receives a request from a different project.
        """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        TRACE_ID_FIELD_NUMBER: builtins.int
        SPAN_ID_FIELD_NUMBER: builtins.int
        TRACE_STATE_FIELD_NUMBER: builtins.int
        ATTRIBUTES_FIELD_NUMBER: builtins.int
        DROPPED_ATTRIBUTES_COUNT_FIELD_NUMBER: builtins.int
        FLAGS_FIELD_NUMBER: builtins.int
        trace_id: builtins.bytes
        """A unique identifier of a trace that this linked span is part of. The ID is a
        16-byte array.
        """
        span_id: builtins.bytes
        """A unique identifier for the linked span. The ID is an 8-byte array."""
        trace_state: builtins.str
        """The trace_state associated with the link."""
        @property
        def attributes(
            self,
        ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
            opentelemetry.proto.common.v1.common_pb2.KeyValue
        ]:
            """attributes is a collection of attribute key/value pairs on the link.
            Attribute keys MUST be unique (it is not allowed to have more than one
            attribute with the same key).
            """
        dropped_attributes_count: builtins.int
        """dropped_attributes_count is the number of dropped attributes. If the value is 0,
        then no attributes were dropped.
        """
        flags: builtins.int
        """Flags, a bit field.

        Bits 0-7 (8 least significant bits) are the trace flags as defined in W3C Trace
        Context specification. To read the 8-bit W3C trace flag, use
        `flags & SPAN_FLAGS_TRACE_FLAGS_MASK`.

        See https://www.w3.org/TR/trace-context-2/#trace-flags for the flag definitions.

        Bits 8 and 9 represent the 3 states of whether the link is remote.
        The states are (unknown, is not remote, is remote).
        To read whether the value is known, use `(flags & SPAN_FLAGS_CONTEXT_HAS_IS_REMOTE_MASK) != 0`.
        To read whether the link is remote, use `(flags & SPAN_FLAGS_CONTEXT_IS_REMOTE_MASK) != 0`.

        Readers MUST NOT assume that bits 10-31 (22 most significant bits) will be zero.
        When creating new spans, bits 10-31 (most-significant 22-bits) MUST be zero.

        [Optional].
        """
        def __init__(
            self,
            *,
            trace_id: builtins.bytes = ...,
            span_id: builtins.bytes = ...,
            trace_state: builtins.str = ...,
            attributes: (
                collections.abc.Iterable[
                    opentelemetry.proto.common.v1.common_pb2.KeyValue
                ]
                | None
            ) = ...,
            dropped_attributes_count: builtins.int = ...,
            flags: builtins.int = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal[
                "attributes",
                b"attributes",
                "dropped_attributes_count",
                b"dropped_attributes_count",
                "flags",
                b"flags",
                "span_id",
                b"span_id",
                "trace_id",
                b"trace_id",
                "trace_state",
                b"trace_state",
            ],
        ) -> None: ...

    TRACE_ID_FIELD_NUMBER: builtins.int
    SPAN_ID_FIELD_NUMBER: builtins.int
    TRACE_STATE_FIELD_NUMBER: builtins.int
    PARENT_SPAN_ID_FIELD_NUMBER: builtins.int
    FLAGS_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    KIND_FIELD_NUMBER: builtins.int
    START_TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    END_TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    DROPPED_ATTRIBUTES_COUNT_FIELD_NUMBER: builtins.int
    EVENTS_FIELD_NUMBER: builtins.int
    DROPPED_EVENTS_COUNT_FIELD_NUMBER: builtins.int
    LINKS_FIELD_NUMBER: builtins.int
    DROPPED_LINKS_COUNT_FIELD_NUMBER: builtins.int
    STATUS_FIELD_NUMBER: builtins.int
    trace_id: builtins.bytes
    """A unique identifier for a trace. All spans from the same trace share
    the same `trace_id`. The ID is a 16-byte array. An ID with all zeroes OR
    of length other than 16 bytes is considered invalid (empty string in OTLP/JSON
    is zero-length and thus is also invalid).

    This field is required.
    """
    span_id: builtins.bytes
    """A unique identifier for a span within a trace, assigned when the span
    is created. The ID is an 8-byte array. An ID with all zeroes OR of length
    other than 8 bytes is considered invalid (empty string in OTLP/JSON
    is zero-length and thus is also invalid).

    This field is required.
    """
    trace_state: builtins.str
    """trace_state conveys information about request position in multiple distributed tracing graphs.
    It is a trace_state in w3c-trace-context format: https://www.w3.org/TR/trace-context/#tracestate-header
    See also https://github.com/w3c/distributed-tracing for more details about this field.
    """
    parent_span_id: builtins.bytes
    """The `span_id` of this span's parent span. If this is a root span, then this
    field must be empty. The ID is an 8-byte array.
    """
    flags: builtins.int
    """Flags, a bit field.

    Bits 0-7 (8 least significant bits) are the trace flags as defined in W3C Trace
    Context specification. To read the 8-bit W3C trace flag, use
    `flags & SPAN_FLAGS_TRACE_FLAGS_MASK`.

    See https://www.w3.org/TR/trace-context-2/#trace-flags for the flag definitions.

    Bits 8 and 9 represent the 3 states of whether a span's parent
    is remote. The states are (unknown, is not remote, is remote).
    To read whether the value is known, use `(flags & SPAN_FLAGS_CONTEXT_HAS_IS_REMOTE_MASK) != 0`.
    To read whether the span is remote, use `(flags & SPAN_FLAGS_CONTEXT_IS_REMOTE_MASK) != 0`.

    When creating span messages, if the message is logically forwarded from another source
    with an equivalent flags fields (i.e., usually another OTLP span message), the field SHOULD
    be copied as-is. If creating from a source that does not have an equivalent flags field
    (such as a runtime representation of an OpenTelemetry span), the high 22 bits MUST
    be set to zero.
    Readers MUST NOT assume that bits 10-31 (22 most significant bits) will be zero.

    [Optional].
    """
    name: builtins.str
    """A description of the span's operation.

    For example, the name can be a qualified method name or a file name
    and a line number where the operation is called. A best practice is to use
    the same display name at the same call point in an application.
    This makes it easier to correlate spans in different traces.

    This field is semantically required to be set to non-empty string.
    Empty value is equivalent to an unknown span name.

    This field is required.
    """
    kind: global___Span.SpanKind.ValueType
    """Distinguishes between spans generated in a particular context. For example,
    two spans with the same name may be distinguished using `CLIENT` (caller)
    and `SERVER` (callee) to identify queueing latency associated with the span.
    """
    start_time_unix_nano: builtins.int
    """start_time_unix_nano is the start time of the span. On the client side, this is the time
    kept by the local machine where the span execution starts. On the server side, this
    is the time when the server's application handler starts running.
    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.

    This field is semantically required and it is expected that end_time >= start_time.
    """
    end_time_unix_nano: builtins.int
    """end_time_unix_nano is the end time of the span. On the client side, this is the time
    kept by the local machine where the span execution ends. On the server side, this
    is the time when the server application handler stops running.
    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.

    This field is semantically required and it is expected that end_time >= start_time.
    """
    @property
    def attributes(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        opentelemetry.proto.common.v1.common_pb2.KeyValue
    ]:
        """attributes is a collection of key/value pairs. Note, global attributes
        like server name can be set using the resource API. Examples of attributes:

            "/http/user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"
            "/http/server_latency": 300
            "example.com/myattribute": true
            "example.com/score": 10.239

        The OpenTelemetry API specification further restricts the allowed value types:
        https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/common/README.md#attribute
        Attribute keys MUST be unique (it is not allowed to have more than one
        attribute with the same key).
        """
    dropped_attributes_count: builtins.int
    """dropped_attributes_count is the number of attributes that were discarded. Attributes
    can be discarded because their keys are too long or because there are too many
    attributes. If this value is 0, then no attributes were dropped.
    """
    @property
    def events(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___Span.Event
    ]:
        """events is a collection of Event items."""
    dropped_events_count: builtins.int
    """dropped_events_count is the number of dropped events. If the value is 0, then no
    events were dropped.
    """
    @property
    def links(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___Span.Link
    ]:
        """links is a collection of Links, which are references from this span to a span
        in the same or different trace.
        """
    dropped_links_count: builtins.int
    """dropped_links_count is the number of dropped links after the maximum size was
    enforced. If this value is 0, then no links were dropped.
    """
    @property
    def status(self) -> global___Status:
        """An optional final status for this span. Semantically when Status isn't set, it means
        span's status code is unset, i.e. assume STATUS_CODE_UNSET (code = 0).
        """

    def __init__(
        self,
        *,
        trace_id: builtins.bytes = ...,
        span_id: builtins.bytes = ...,
        trace_state: builtins.str = ...,
        parent_span_id: builtins.bytes = ...,
        flags: builtins.int = ...,
        name: builtins.str = ...,
        kind: global___Span.SpanKind.ValueType = ...,
        start_time_unix_nano: builtins.int = ...,
        end_time_unix_nano: builtins.int = ...,
        attributes: (
            collections.abc.Iterable[
                opentelemetry.proto.common.v1.common_pb2.KeyValue
            ]
            | None
        ) = ...,
        dropped_attributes_count: builtins.int = ...,
        events: collections.abc.Iterable[global___Span.Event] | None = ...,
        dropped_events_count: builtins.int = ...,
        links: collections.abc.Iterable[global___Span.Link] | None = ...,
        dropped_links_count: builtins.int = ...,
        status: global___Status | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["status", b"status"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "attributes",
            b"attributes",
            "dropped_attributes_count",
            b"dropped_attributes_count",
            "dropped_events_count",
            b"dropped_events_count",
            "dropped_links_count",
            b"dropped_links_count",
            "end_time_unix_nano",
            b"end_time_unix_nano",
            "events",
            b"events",
            "flags",
            b"flags",
            "kind",
            b"kind",
            "links",
            b"links",
            "name",
            b"name",
            "parent_span_id",
            b"parent_span_id",
            "span_id",
            b"span_id",
            "start_time_unix_nano",
            b"start_time_unix_nano",
            "status",
            b"status",
            "trace_id",
            b"trace_id",
            "trace_state",
            b"trace_state",
        ],
    ) -> None: ...

global___Span = Span

@typing_extensions.final
class Status(google.protobuf.message.Message):
    """The Status type defines a logical error model that is suitable for different
    programming environments, including REST APIs and RPC APIs.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _StatusCode:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _StatusCodeEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
            Status._StatusCode.ValueType
        ],
        builtins.type,
    ):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        STATUS_CODE_UNSET: Status._StatusCode.ValueType  # 0
        """The default status."""
        STATUS_CODE_OK: Status._StatusCode.ValueType  # 1
        """The Span has been validated by an Application developer or Operator to 
        have completed successfully.
        """
        STATUS_CODE_ERROR: Status._StatusCode.ValueType  # 2
        """The Span contains an error."""

    class StatusCode(_StatusCode, metaclass=_StatusCodeEnumTypeWrapper):
        """For the semantics of status codes see
        https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/trace/api.md#set-status
        """

    STATUS_CODE_UNSET: Status.StatusCode.ValueType  # 0
    """The default status."""
    STATUS_CODE_OK: Status.StatusCode.ValueType  # 1
    """The Span has been validated by an Application developer or Operator to 
    have completed successfully.
    """
    STATUS_CODE_ERROR: Status.StatusCode.ValueType  # 2
    """The Span contains an error."""

    MESSAGE_FIELD_NUMBER: builtins.int
    CODE_FIELD_NUMBER: builtins.int
    message: builtins.str
    """A developer-facing human readable error message."""
    code: global___Status.StatusCode.ValueType
    """The status code."""
    def __init__(
        self,
        *,
        message: builtins.str = ...,
        code: global___Status.StatusCode.ValueType = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "code", b"code", "message", b"message"
        ],
    ) -> None: ...

global___Status = Status
